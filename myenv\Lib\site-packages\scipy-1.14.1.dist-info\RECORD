scipy-1.14.1-cp311-cp311-win_amd64.whl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.14.1.dist-info/DELVEWHEEL,sha256=t5f8HJnql3DlfxtBoMGa2fmYnlHY6FmoXcz3WAgdzwU,576
scipy-1.14.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scipy-1.14.1.dist-info/LICENSE.txt,sha256=UlkCiee0Uvv9lJ2s9Q4oE-NhKrt8EW5cegwnpJa5xe8,47742
scipy-1.14.1.dist-info/METADATA,sha256=vQr3zFZ9UDDler_LOflZDjqd1lZvxNgY_OnYrP9xfok,60778
scipy-1.14.1.dist-info/RECORD,,
scipy-1.14.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.14.1.dist-info/WHEEL,sha256=JdLTWhc73oJ-lqTBYGgiVontr_vhzwzbpAOin_2bxTI,85
scipy.libs/libscipy_openblas-5b1ec8b915dfb81d11cebc0788069d2d.dll,sha256=mWT4k3t-AwfHOEBzVocxvJd4FnaK75NIkG6MzlliVNU,38190043
scipy/__config__.py,sha256=XKYsVGOknmqK9WQ7sJpWkUTK3Opza3fIoRa0IVyb3bM,5654
scipy/__init__.py,sha256=SbSFJwNw9YG1hY0hTU2M8cTuTT2N73ymjRQSqAtdSPI,4615
scipy/__pycache__/__config__.cpython-311.pyc,,
scipy/__pycache__/__init__.cpython-311.pyc,,
scipy/__pycache__/_distributor_init.cpython-311.pyc,,
scipy/__pycache__/conftest.cpython-311.pyc,,
scipy/__pycache__/version.cpython-311.pyc,,
scipy/_distributor_init.py,sha256=CmoJiFF1KyM0MvnPov6kBTO60D43AVlYsfYSmaSuoZY,629
scipy/_lib/__init__.py,sha256=lcHbxe9IkuoUZjXA8VX6-SYYCREBLq1cqoPo2mR6V1w,367
scipy/_lib/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/__pycache__/_array_api.cpython-311.pyc,,
scipy/_lib/__pycache__/_bunch.cpython-311.pyc,,
scipy/_lib/__pycache__/_ccallback.cpython-311.pyc,,
scipy/_lib/__pycache__/_disjoint_set.cpython-311.pyc,,
scipy/_lib/__pycache__/_docscrape.cpython-311.pyc,,
scipy/_lib/__pycache__/_elementwise_iterative_method.cpython-311.pyc,,
scipy/_lib/__pycache__/_finite_differences.cpython-311.pyc,,
scipy/_lib/__pycache__/_gcutils.cpython-311.pyc,,
scipy/_lib/__pycache__/_pep440.cpython-311.pyc,,
scipy/_lib/__pycache__/_testutils.cpython-311.pyc,,
scipy/_lib/__pycache__/_threadsafety.cpython-311.pyc,,
scipy/_lib/__pycache__/_tmpdirs.cpython-311.pyc,,
scipy/_lib/__pycache__/_util.cpython-311.pyc,,
scipy/_lib/__pycache__/decorator.cpython-311.pyc,,
scipy/_lib/__pycache__/deprecation.cpython-311.pyc,,
scipy/_lib/__pycache__/doccer.cpython-311.pyc,,
scipy/_lib/__pycache__/uarray.cpython-311.pyc,,
scipy/_lib/_array_api.py,sha256=cRGRC91mCTPJblwvB8xZHzrE7RGlgfAB3CHJjLE2GP8,19156
scipy/_lib/_bunch.py,sha256=wQK9j5N61P1eXt73dEZnUcEc8bS5Y59qOgp_av6eGPc,8345
scipy/_lib/_ccallback.py,sha256=RDz5WUY_jgPtUlEgtm-VGxcub9nvF4laqIsyExK8Nzk,7338
scipy/_lib/_ccallback_c.cp311-win_amd64.dll.a,sha256=DG04AfJ4rq5DFQVM5Y7WpbolkHytxXaIZbIyXWi-ZtU,1608
scipy/_lib/_ccallback_c.cp311-win_amd64.pyd,sha256=nQWOWuvz06GpvmAph1wc0GJP-CzbotVzuvWM8tjN3bk,86016
scipy/_lib/_disjoint_set.py,sha256=3EIkZl2z9UajnPRVjSD5H8JszidKURpxHCO580OJGC8,6414
scipy/_lib/_docscrape.py,sha256=5dCdfJJwgOz_Utb7OrSkiEmeCDxCm-Xs6kYLbQu_fEQ,22177
scipy/_lib/_elementwise_iterative_method.py,sha256=2p2C2O_dyfy767F9112KkM4ZkKJT3mOb0tHWpqY7wok,15070
scipy/_lib/_finite_differences.py,sha256=Uu28sJ1PNvgW0Y7ESs0voon5AVYtj0QRrbqsVPjoL70,4317
scipy/_lib/_fpumode.cp311-win_amd64.dll.a,sha256=_tW-34cgxhrMiAKjGFk1sJmZsJZgUAvs5M5C76ORNeQ,1560
scipy/_lib/_fpumode.cp311-win_amd64.pyd,sha256=vs0Mekt7elxl0JJHfAMK43V3aIVnt1CHxZEg5fOlU4Y,15872
scipy/_lib/_gcutils.py,sha256=ULLIREutjgyo8dLsvU2hhI0lDGeu5LqX6557X60xPi0,2774
scipy/_lib/_pep440.py,sha256=xSupJHSD_X9EOOUla1iKcNZ9lwxW6qraE3cbHHLunBY,14492
scipy/_lib/_test_ccallback.cp311-win_amd64.dll.a,sha256=03iaHvOKQiX5mgHo73FYQdRPFs9ZgZ6sDcyRWBsoyKQ,1640
scipy/_lib/_test_ccallback.cp311-win_amd64.pyd,sha256=1vd7pOX4KPpd6CxGESglkQzZZOoUTJxHDyFMduuE8-g,52224
scipy/_lib/_test_deprecation_call.cp311-win_amd64.dll.a,sha256=7keBoqO9Mex1QdgYH_i7IUypP1xNuZXTHyYTrM6hFI4,1724
scipy/_lib/_test_deprecation_call.cp311-win_amd64.pyd,sha256=661dcpDNXknVGtia1cEdwr8lALl7Stz-9snhDDCSm2A,34816
scipy/_lib/_test_deprecation_def.cp311-win_amd64.dll.a,sha256=VWCf59U83b2QvE9tZqgki0rC_Kv_xJ5mjU99YhW1aJE,1712
scipy/_lib/_test_deprecation_def.cp311-win_amd64.pyd,sha256=UkhRB9M-RjZfCkRENpDz988yl0qoCw1FBSOm_pCDvyY,27136
scipy/_lib/_testutils.py,sha256=CL7uCu-RiTUnkbsjFT_rZPGqP4OjmaUOdA8pghCe-ks,11530
scipy/_lib/_threadsafety.py,sha256=2dkby9bQV_BrdlThHUfiqZJsQq-Mte5R28_ueFpNikA,1513
scipy/_lib/_tmpdirs.py,sha256=r8sz8dcfBYiPAYondvpGHVVFB2_rB5sajkTeV1gt1fw,2460
scipy/_lib/_uarray/LICENSE,sha256=5MSswx6ChwZtfJldJuPvkCyhl37hh7JgsRwng2ZQOBE,1543
scipy/_lib/_uarray/__init__.py,sha256=pex73GgY7YUWARVgHzYw_Ky2fdAH7M9BcuDMif_hm38,4609
scipy/_lib/_uarray/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/_uarray/__pycache__/_backend.cpython-311.pyc,,
scipy/_lib/_uarray/_backend.py,sha256=bKJqAjPtPrnHUpph4Po4rSvN7V9cRKRZVVOsExkV9x0,21136
scipy/_lib/_uarray/_uarray.cp311-win_amd64.dll.a,sha256=9Za5e4aOqTnCMvqWWOjp4yXyEpkatKjjx7KITW5MdIQ,1544
scipy/_lib/_uarray/_uarray.cp311-win_amd64.pyd,sha256=fb1v2ZM_hhw7pcnFaEULkARyXxN7-M45tnyxThYc05Q,233472
scipy/_lib/_util.py,sha256=qKpgorKcWQ6ixklWFzuHa4p6Ztgprzoo8RAfCdrn42E,33442
scipy/_lib/array_api_compat/__init__.py,sha256=MTXJSoYcZo2NOsSqj19IQUVmYTyKst5vMZuK55LpGSk,991
scipy/_lib/array_api_compat/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/__pycache__/_internal.cpython-311.pyc,,
scipy/_lib/array_api_compat/_internal.py,sha256=h_DFinHYX8hkUskb3-vE-mVsrE8lAYXhE8VmfTNn1RE,1056
scipy/_lib/array_api_compat/common/__init__.py,sha256=Pcc7izwxYn16qGUghaEELBrpxo-jwxvMA84eAxuAYco,38
scipy/_lib/array_api_compat/common/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_helpers.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_typing.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/_aliases.py,sha256=o8DMiulwyqt32254wufHKnX_1FkVR6_HBUHdMtt0yjQ,17541
scipy/_lib/array_api_compat/common/_fft.py,sha256=5pov37o1Fo4kddw3n09UlrPsYM0acM6hfzmt9-1xhCA,4703
scipy/_lib/array_api_compat/common/_helpers.py,sha256=p5jkL7Ve0pnqoJ0Aa9sotkUzVimErrDrMOP5KjsWshw,16088
scipy/_lib/array_api_compat/common/_linalg.py,sha256=gynAATUciYw20fuFasi6mg37-B_p7a6-KsPatPx5fyA,6462
scipy/_lib/array_api_compat/common/_typing.py,sha256=tg-oqD7YZ8bkUDw06Nz3yzsLbMDycZ1ZSREFSsT7Fds,437
scipy/_lib/array_api_compat/cupy/__init__.py,sha256=O8IV979bIWiSfmDEYh6U-GK_mr0YZRllbKSNCsDVLvU,458
scipy/_lib/array_api_compat/cupy/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_typing.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/_aliases.py,sha256=XWdKHrQghFt89VnfLgs3AFuDpfsL23e1YFMUf75aBk4,2715
scipy/_lib/array_api_compat/cupy/_typing.py,sha256=VhPA4g6G5Y8acUHqd5XG_vABH_M5mHUajSqFFfesBgM,663
scipy/_lib/array_api_compat/cupy/fft.py,sha256=9Uq43Ykr7VXkYooSFkPDAjgMYv8wC_s-FBrVn3ncSFw,878
scipy/_lib/array_api_compat/cupy/linalg.py,sha256=Oc-wZ7mgrnVMNMxGAr0uK6MFTFx6ctzHgYoEEgM2-vo,1493
scipy/_lib/array_api_compat/dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/array_api_compat/dask/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__init__.py,sha256=kMRvJJmxKpyYIwQhqiJGhJpD9YjPDcjxOf-kgcW8uoc,217
scipy/_lib/array_api_compat/dask/array/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/_aliases.py,sha256=NY7XXQSMHK7xN6YDuGUu3BafIV188hltbdjs0m5QCeM,4287
scipy/_lib/array_api_compat/dask/array/linalg.py,sha256=GtNTq1bT2xng605JeW39GVt3u-kQRghtZa8aX_UPuws,2486
scipy/_lib/array_api_compat/numpy/__init__.py,sha256=vDSPITfPOJccPSELlBxQhg6ZiJfduFByU5Ep9IAfQLM,719
scipy/_lib/array_api_compat/numpy/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_typing.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/_aliases.py,sha256=vI26ROevPCzy9GXUXF-FXixSosZKBOQTwxUYm79w2MM,2719
scipy/_lib/array_api_compat/numpy/_typing.py,sha256=m3A_ClFAyC17Hbsm77fe-eSfXbOFpx5h9_WggniIN5A,664
scipy/_lib/array_api_compat/numpy/fft.py,sha256=vqmmXzSZkWlC5V9VB1DO4Uhyrr_BVaC6MRZjihSJ3CY,708
scipy/_lib/array_api_compat/numpy/linalg.py,sha256=Y4TL378Z6eRW1qzDgbI114CMdQ6-VNPX31V8xCIZt9k,3346
scipy/_lib/array_api_compat/torch/__init__.py,sha256=9OFoHx-HEUVVlu4WkdI0cQIdgeB_VY1YBbOxMXn2pGs,615
scipy/_lib/array_api_compat/torch/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/_aliases.py,sha256=5cFKD31pQUar3Tp_afkwwnAC82spYm28-GJS4O6vTQo,27938
scipy/_lib/array_api_compat/torch/fft.py,sha256=DcBZjX0641nT5MZ1IQ0Cjk74fAcjCHzXfNFDiGHQkQc,1880
scipy/_lib/array_api_compat/torch/linalg.py,sha256=e2RiqrOwmPfrwdKoauOx5qhT4krzC2pXMVP5VZ7sEXU,3663
scipy/_lib/cobyqa/__init__.py,sha256=ByI6PQQ2LOQotkAsxhYOnE354sZY-CkmNgzgoiw2h34,598
scipy/_lib/cobyqa/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/framework.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/main.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/models.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/problem.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/settings.cpython-311.pyc,,
scipy/_lib/cobyqa/framework.py,sha256=lsQJmi7PPZrUOn6j0WAVNsHvFFM2W--dMdnnDzFPtcg,40112
scipy/_lib/cobyqa/main.py,sha256=oBHNzlEY5WXz-RUBrVy90VsYLS5n6hqSeyy6XYpSsAU,57985
scipy/_lib/cobyqa/models.py,sha256=kUGbK8bl1j4mwTnokhYKiIDE-G1ySSzZJaj22-GQAUc,51991
scipy/_lib/cobyqa/problem.py,sha256=nSIcGBXfb4H_ksIJW5ywHhC40kyTDoTxS7mVpN5ocio,41101
scipy/_lib/cobyqa/settings.py,sha256=fWu0NPyFiBE-8No4R9B4pUKSjEbuSlaa0AWtHIzHblA,3958
scipy/_lib/cobyqa/subsolvers/__init__.py,sha256=96yleneJh-YtMtCa8tG8cfsNM-Y_1lF30jJ5tnIKIQg,355
scipy/_lib/cobyqa/subsolvers/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/cobyqa/subsolvers/__pycache__/geometry.cpython-311.pyc,,
scipy/_lib/cobyqa/subsolvers/__pycache__/optim.cpython-311.pyc,,
scipy/_lib/cobyqa/subsolvers/geometry.py,sha256=1jO30o2Nm4BiKpb-GAv8Uqj1SnDNLpo9J7ECoOUdP3o,14560
scipy/_lib/cobyqa/subsolvers/optim.py,sha256=-tJVfZcS66vfQYri293E4Bx710VqYT-N00bYN6vpo9I,46715
scipy/_lib/cobyqa/utils/__init__.py,sha256=dyc27b5M6bGiH7f1laeaoqaQeEKlKvFevVghBV7LrtY,377
scipy/_lib/cobyqa/utils/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/cobyqa/utils/__pycache__/exceptions.cpython-311.pyc,,
scipy/_lib/cobyqa/utils/__pycache__/math.cpython-311.pyc,,
scipy/_lib/cobyqa/utils/__pycache__/versions.cpython-311.pyc,,
scipy/_lib/cobyqa/utils/exceptions.py,sha256=rFMjALTKz9xNnb4pTS5v_VIXqh0T7QpwlkK8YUeN000,505
scipy/_lib/cobyqa/utils/math.py,sha256=QhsfzOYAJnLtViuMAXKNQwpRd5ZKMNs_faeJ3TKQdnw,1688
scipy/_lib/cobyqa/utils/versions.py,sha256=S2JG4-zryLBoBf2yzVlIRs1sW6eMUeWFfcjWn2D39jg,1532
scipy/_lib/decorator.py,sha256=JoJOywg9I_xfdfasNhrwiasJEnNTvV3SYGv6GtC1Mak,15439
scipy/_lib/deprecation.py,sha256=L6_eIt28ZIJf3vlVMRXJl4cmbUhdMVZhwz00Ty_Tqq0,8313
scipy/_lib/doccer.py,sha256=9Ins8j58b2YQkD-vWCH1nMJ7pklhhRWqjELTeiJ-a_w,8637
scipy/_lib/messagestream.cp311-win_amd64.dll.a,sha256=V4QGXxLRz9W9kdZ6PWcWuaTFrMXku--tEHbb7LwTbZU,1616
scipy/_lib/messagestream.cp311-win_amd64.pyd,sha256=GS84rlfHt0iSsUN942PDRgPLlmJRLwUtBVPvfNQGuuA,66048
scipy/_lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__gcutils.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__pep440.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__testutils.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__threadsafety.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__util.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_array_api.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_bunch.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_ccallback.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_deprecation.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_import_cycles.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_public_api.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_scipy_version.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_tmpdirs.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_warnings.cpython-311.pyc,,
scipy/_lib/tests/test__gcutils.py,sha256=EeeSFQ1MTmcgxcDD5yI16UUAfESjSx40n3prcogOX30,3517
scipy/_lib/tests/test__pep440.py,sha256=4AS6UhkhxfVqvZfZmxM0pgIOvQVbAjPdWN3YtgfXWkY,2344
scipy/_lib/tests/test__testutils.py,sha256=cI6oLinJsZDXIi_pmPMTVAYTvepddvhYVGEMd2MlW2Q,832
scipy/_lib/tests/test__threadsafety.py,sha256=nnAypNwYK4YNe2s3xcYfGslLKXqdcrOeza73EeybiAQ,1373
scipy/_lib/tests/test__util.py,sha256=Jshr26XVtlBCwc5lZVoJU9DqRcI8dcc2BDLIVJNeQ_w,16642
scipy/_lib/tests/test_array_api.py,sha256=diQDdP_9jasIeI3C8h3MyIz7-2ya7J0vaTayvdPRSEo,4363
scipy/_lib/tests/test_bunch.py,sha256=yZmGHnJ-qBIMP-8TmnyvK7OGiXRzHzbRHCpMwIsmsk0,6330
scipy/_lib/tests/test_ccallback.py,sha256=klbEMLd28QAUyzGp6Z89Fr6FzV6jKnmuQumzpe4bXco,6379
scipy/_lib/tests/test_deprecation.py,sha256=NGUuuv24fSTGyTUZWd2saZkapR2NOpIpx0tijRjBQ7Y,374
scipy/_lib/tests/test_import_cycles.py,sha256=00cZgGqI7LC5_Zup6TRm3MNUkNzDO4D2eGh2WqNIYNs,576
scipy/_lib/tests/test_public_api.py,sha256=ZaX4lwrnvpW5cedPlWMvtGMtKsal33VZYD2E3tl5x7c,19905
scipy/_lib/tests/test_scipy_version.py,sha256=j-i3VewqD2gfdZZiJQCdlB3x_4EVekZkND1IFWszqhc,624
scipy/_lib/tests/test_tmpdirs.py,sha256=jY1yJyn2NN6l_BX_7u5HOuohD9K_SlU08W_yaoR15ek,1282
scipy/_lib/tests/test_warnings.py,sha256=PYkWD6MS4ljrUP3pxl2Bo8bRC_VERBPwaw50AsMGWec,4966
scipy/_lib/uarray.py,sha256=qXvvUluJiQJwLybyo5ZZtGGWa_o2T0mSvojeu8t_IkQ,846
scipy/cluster/__init__.py,sha256=ck3TgyUyHOG1-MiymZd04JoIvkWrBaFL56fM-LS-tK8,907
scipy/cluster/__pycache__/__init__.cpython-311.pyc,,
scipy/cluster/__pycache__/hierarchy.cpython-311.pyc,,
scipy/cluster/__pycache__/vq.cpython-311.pyc,,
scipy/cluster/_hierarchy.cp311-win_amd64.dll.a,sha256=Sg_JeLalo2V-c5Kr0caiY76OtD1cPXotLvAUn9XxC98,1580
scipy/cluster/_hierarchy.cp311-win_amd64.pyd,sha256=RHYbmJo-8tqykzV1jmP15IZMyAvV6scXbxkAs31XmCg,380928
scipy/cluster/_optimal_leaf_ordering.cp311-win_amd64.dll.a,sha256=F2TUvO9-aQOCXX73-FvP7R3usaEET4P0jEJ7-9wzWuk,1724
scipy/cluster/_optimal_leaf_ordering.cp311-win_amd64.pyd,sha256=i06Aqw4taWZ7wudyNABZYO8uBfyHdR4VAddL3UbWrtU,327168
scipy/cluster/_vq.cp311-win_amd64.dll.a,sha256=g7oF-4EaFsBnxSn9brFRsMhdEHHJR2MqhB-OCMN_UX8,1496
scipy/cluster/_vq.cp311-win_amd64.pyd,sha256=LFXtNgp8N6G2snjlt0qH23yVY-SfC8lbPMS4Ci9dnwI,110080
scipy/cluster/hierarchy.py,sha256=30LPpX8QDftwDAuWNBsDIq4WmWZ6bWzBIT8K-TqmeKU,152905
scipy/cluster/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/cluster/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/cluster/tests/__pycache__/hierarchy_test_data.cpython-311.pyc,,
scipy/cluster/tests/__pycache__/test_disjoint_set.cpython-311.pyc,,
scipy/cluster/tests/__pycache__/test_hierarchy.cpython-311.pyc,,
scipy/cluster/tests/__pycache__/test_vq.cpython-311.pyc,,
scipy/cluster/tests/hierarchy_test_data.py,sha256=lvVMk2L5iEKlhX32aFRBJ3clecx6AXLYw75Epl07OBo,6995
scipy/cluster/tests/test_disjoint_set.py,sha256=fczIL8hK_ICE23j54dwEdn9KBOipPnpxAhWK7xj8X4o,5727
scipy/cluster/tests/test_hierarchy.py,sha256=lRHvDjdbTfKb4g1twP02nGw_izw8-HwMMw3LptPTkLY,51875
scipy/cluster/tests/test_vq.py,sha256=cnNm6D3zyASgV_oYDhGbGTtVZL846jyoN-N1sVSSvxQ,18729
scipy/cluster/vq.py,sha256=L6Q47NKwdW7grMT_JSAa1zqU7IMVVQUfDCyD9zRUlSM,31573
scipy/conftest.py,sha256=7R9hHKaWNE3SWg2CAYbU18bQ1dIFWuvQwRN7tFii458,16699
scipy/constants/__init__.py,sha256=21Fu1jN-LvN-EbuCnzxGTQgAiv33VwvKM2PcDM7WeHc,12784
scipy/constants/__pycache__/__init__.cpython-311.pyc,,
scipy/constants/__pycache__/_codata.cpython-311.pyc,,
scipy/constants/__pycache__/_constants.cpython-311.pyc,,
scipy/constants/__pycache__/codata.cpython-311.pyc,,
scipy/constants/__pycache__/constants.cpython-311.pyc,,
scipy/constants/_codata.py,sha256=R3T8P1TLrAnQV9WCYF8c9OriLHtGmtp0vQ15PmT45cI,157383
scipy/constants/_constants.py,sha256=29sKBB9M_f6nTVs9VpUGYnUNYpFCbUDyPiyU7RsONJw,10877
scipy/constants/codata.py,sha256=Fayz-HkpNqk8aOadBz7AfFpKinS4JcYhKBm_JUweY-o,635
scipy/constants/constants.py,sha256=vdC_jDTQZWQlrkYZY0Uj7NKO2AtBwH5mc28Jhy7Ur9s,2303
scipy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/constants/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/constants/tests/__pycache__/test_codata.cpython-311.pyc,,
scipy/constants/tests/__pycache__/test_constants.cpython-311.pyc,,
scipy/constants/tests/test_codata.py,sha256=0p4cAPH0UMK3JWHGI2Ha05dQpxBOzNSme3_pWcIVTDw,2016
scipy/constants/tests/test_constants.py,sha256=xBHRrUN8kY5MVOHO8XxextP8VBiBlDq_t3qrs1gzQSk,4728
scipy/datasets/__init__.py,sha256=9FDldqGVaM-sobI1bN43qJeNppyBd-W48hpEEjnCHqg,2892
scipy/datasets/__pycache__/__init__.cpython-311.pyc,,
scipy/datasets/__pycache__/_download_all.cpython-311.pyc,,
scipy/datasets/__pycache__/_fetchers.cpython-311.pyc,,
scipy/datasets/__pycache__/_registry.cpython-311.pyc,,
scipy/datasets/__pycache__/_utils.cpython-311.pyc,,
scipy/datasets/_download_all.py,sha256=i-fMQWgAfvOgCEoEabsd68iUYDki-4OUfPjjcGLHRDI,1758
scipy/datasets/_fetchers.py,sha256=BFNaeFZRuk2EaJaUu86Di7I4GQgbzXrMtok6bVWh7JQ,6957
scipy/datasets/_registry.py,sha256=3_ZNYpe3lCXvUGalpNsrhRwKcnFA2P6yWV7MFdKb2Hw,1098
scipy/datasets/_utils.py,sha256=7bK_NVCoURt-HDGe7I0iBGLEdcrLIU5eJuSjJqAkgJg,3048
scipy/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/datasets/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/datasets/tests/__pycache__/test_data.cpython-311.pyc,,
scipy/datasets/tests/test_data.py,sha256=NtcORELzW3ua90cd-fNUZeBPtPktAiBOm2JrwwpNgYQ,4218
scipy/fft/__init__.py,sha256=Sn1qqeuX6MkMiU7eRyFMEE3ekkecR5Sht-WK-dWaPvk,3746
scipy/fft/__pycache__/__init__.cpython-311.pyc,,
scipy/fft/__pycache__/_backend.cpython-311.pyc,,
scipy/fft/__pycache__/_basic.cpython-311.pyc,,
scipy/fft/__pycache__/_basic_backend.cpython-311.pyc,,
scipy/fft/__pycache__/_debug_backends.cpython-311.pyc,,
scipy/fft/__pycache__/_fftlog.cpython-311.pyc,,
scipy/fft/__pycache__/_fftlog_backend.cpython-311.pyc,,
scipy/fft/__pycache__/_helper.cpython-311.pyc,,
scipy/fft/__pycache__/_realtransforms.cpython-311.pyc,,
scipy/fft/__pycache__/_realtransforms_backend.cpython-311.pyc,,
scipy/fft/_backend.py,sha256=ySd0v6IodaTh-_kjfEOogdlfCsJ3rdcRLqv-ZU8NkTM,6740
scipy/fft/_basic.py,sha256=pSqks7ZcODqlyWVorO1pg3C_GyfcQxVdgUSHvNTTHmg,64627
scipy/fft/_basic_backend.py,sha256=58M3rP3tMyW5-bqogbjetW4j5tiad6Ks9MpB9vViI-I,6830
scipy/fft/_debug_backends.py,sha256=R0zCc_CgDubYnaGbQGjk0GTmd5HlYFpBLw7WSlqljpQ,620
scipy/fft/_fftlog.py,sha256=YKigZKxZIgBlFDzvJyMgnpY8cgA72ax7tFr8OhnoZkI,8089
scipy/fft/_fftlog_backend.py,sha256=Vn77HqfBYOaWaj1HAWsgAIAzOXaWmZIs3nyFkWTO8mw,5480
scipy/fft/_helper.py,sha256=zESzcOKqLJSM0cBjhZPp4Yh0kMjBSjZnAy7eE_rv0O4,12764
scipy/fft/_pocketfft/LICENSE.md,sha256=wEZhaFz_nYA1_hxMOrNX5_djPK_AanPp2Qw8X-3oqGo,1523
scipy/fft/_pocketfft/__init__.py,sha256=WKXb59wx9DK9zXq9DwKchp-zg1SuJIynTQ_POQrFXxo,216
scipy/fft/_pocketfft/__pycache__/__init__.cpython-311.pyc,,
scipy/fft/_pocketfft/__pycache__/basic.cpython-311.pyc,,
scipy/fft/_pocketfft/__pycache__/helper.cpython-311.pyc,,
scipy/fft/_pocketfft/__pycache__/realtransforms.cpython-311.pyc,,
scipy/fft/_pocketfft/basic.py,sha256=dST8PBFhoiOR1Kj1j3CcjMC0p7KFxTA9qDr1oN2YrFw,8389
scipy/fft/_pocketfft/helper.py,sha256=ecKFAIxeXXxMJllrRIbkPdETQ6Sdq552ozKJEaCG80g,6062
scipy/fft/_pocketfft/pypocketfft.cp311-win_amd64.dll.a,sha256=NqXDL4nLUSPKmjQC_nMRLbxr4OdL4sLRo3VlLQaKEdM,1592
scipy/fft/_pocketfft/pypocketfft.cp311-win_amd64.pyd,sha256=k9g15E_oDvby790bw9nRiTYiNsC9LHS1PFVH9yFstig,1080832
scipy/fft/_pocketfft/realtransforms.py,sha256=nk1e31laxa9920AeBgZgb1vZSXIpoILwWqSI028HCyQ,3453
scipy/fft/_pocketfft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/_pocketfft/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_real_transforms.cpython-311.pyc,,
scipy/fft/_pocketfft/tests/test_basic.py,sha256=Jy59AaqVo2qdunyKB7bfXeCOTXnkqBx4oo7TPRpBpc0,36378
scipy/fft/_pocketfft/tests/test_real_transforms.py,sha256=EBq5FEvMEVSyczizYsXnPBNIlPALwmVSAPzaUAmZss4,17150
scipy/fft/_realtransforms.py,sha256=ba4SZIlpA405Ujt5lEo2RngO3zsoJJS0259t7nC23_s,26079
scipy/fft/_realtransforms_backend.py,sha256=hJ3LkFOvggibVF_E36fM9R3ZqAJHHicYrGUceH8B81g,2452
scipy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/fft/tests/__pycache__/mock_backend.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_backend.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_fftlog.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_helper.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_multithreading.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_real_transforms.cpython-311.pyc,,
scipy/fft/tests/mock_backend.py,sha256=jlJZ-GOwaAmSjp6g_riFY9X_86a2ngqmhOt-mUF0SGg,2646
scipy/fft/tests/test_backend.py,sha256=MW6yc0GGrT0tGuiXX-AxZXUDwUHJZ9qIoYS40w_tlJs,4373
scipy/fft/tests/test_basic.py,sha256=-LY5uapJZ-d_CSyEjhonwRq0y_bpZRLkeMMJ16pPK1Q,20772
scipy/fft/tests/test_fftlog.py,sha256=YvkXw-dPmpQbLG-0Y6M0m7gYi4J6a4yYVNeeWUtJRgM,6814
scipy/fft/tests/test_helper.py,sha256=3zMP336-yeIB5V-d7agAxcf7k2Fb6x5v3G8x3am-h44,20606
scipy/fft/tests/test_multithreading.py,sha256=hMqYEawnqaoCS3fp6dOSLBn1l4OR6D7txckjod8vipc,2234
scipy/fft/tests/test_real_transforms.py,sha256=tvQP0jmfdQIy5CFz8rWJO3PkL_-cnV_5CoUC469QIdo,9547
scipy/fftpack/__init__.py,sha256=NKSnRJ6EMDP3nDxGSJMyd2PJBB2ytMAO01Kq884NXXo,3258
scipy/fftpack/__pycache__/__init__.cpython-311.pyc,,
scipy/fftpack/__pycache__/_basic.cpython-311.pyc,,
scipy/fftpack/__pycache__/_helper.cpython-311.pyc,,
scipy/fftpack/__pycache__/_pseudo_diffs.cpython-311.pyc,,
scipy/fftpack/__pycache__/_realtransforms.cpython-311.pyc,,
scipy/fftpack/__pycache__/basic.cpython-311.pyc,,
scipy/fftpack/__pycache__/helper.cpython-311.pyc,,
scipy/fftpack/__pycache__/pseudo_diffs.cpython-311.pyc,,
scipy/fftpack/__pycache__/realtransforms.cpython-311.pyc,,
scipy/fftpack/_basic.py,sha256=6AFPL2JeBvZsIZV-C3kAmvG3ybQ8XtiRLSJQ9Go9L2E,13526
scipy/fftpack/_helper.py,sha256=KTS-_cTl2lNu8PXdopJRG80EbwBI3HnhvvziiWRT2tE,3465
scipy/fftpack/_pseudo_diffs.py,sha256=5IZuPf96AqFK17zyOv3PJUICEd5qrzQPqW7Aq-B-Pa8,14751
scipy/fftpack/_realtransforms.py,sha256=gG8Q39JAiO1Y3k8neJR1rfXLNhb9IvfBn7w8URM-Ndw,19812
scipy/fftpack/basic.py,sha256=FFn2KxrsmC6IsOQdjcoVr8Nvrlng2FRiv7gNeT1ZrY4,597
scipy/fftpack/convolve.cp311-win_amd64.dll.a,sha256=0MLwnEweQcFPL9gwTeCeXf0xKuHVuqtRDF4gAIDmxuc,1560
scipy/fftpack/convolve.cp311-win_amd64.pyd,sha256=y87cFYcclmPAjruVGR0hNN4lZ4F2ywbALKj25KG70xo,250880
scipy/fftpack/helper.py,sha256=1b1b278FWyTc2MeAjeLFB8eyV76pRxOigGtBUvCp_lo,599
scipy/fftpack/pseudo_diffs.py,sha256=oLCcXufpR_wAj4TKj3OTqyIEGXag1xSl_ueO83XspMI,680
scipy/fftpack/realtransforms.py,sha256=oUJXNb5KAyS4k8xubnE7hGE9BpLCcdkk_iiReyB8OOE,614
scipy/fftpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fftpack/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_helper.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_import.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_pseudo_diffs.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_real_transforms.cpython-311.pyc,,
scipy/fftpack/tests/fftw_double_ref.npz,sha256=pgxklBW2RSI5JNg0LMxcCXgByGkBKHo2nlP8kln17E4,162120
scipy/fftpack/tests/fftw_longdouble_ref.npz,sha256=pAbL1NrQTQxZ3Tj1RBb7SUJMgiKcGgdLakTsDN4gAOM,296072
scipy/fftpack/tests/fftw_single_ref.npz,sha256=J2qRQTGOb8NuSrb_VKYbZAVO-ISbZg8XNZ5fVBtDxSY,95144
scipy/fftpack/tests/test.npz,sha256=Nt6ASiLY_eoFRZDOSd3zyFmDi32JGTxWs7y2YMv0N5c,11968
scipy/fftpack/tests/test_basic.py,sha256=j3VoREWfwuTJF3vbCGcqUoL4Dt-L9ehKKE6KCQDAfWs,31180
scipy/fftpack/tests/test_helper.py,sha256=HuCAP5esSm4lMnw9f9i-ByJj1DURntgC64s2fX5RVIg,1729
scipy/fftpack/tests/test_import.py,sha256=v63sC7YHLiOcaMWZ75_t0Nasz2Q8Tq-lo7awD4CINwI,1189
scipy/fftpack/tests/test_pseudo_diffs.py,sha256=5wmGeVj5QXbFkxrRG7Tl162v4pFSxiMSONtm4Uoy-AQ,13769
scipy/fftpack/tests/test_real_transforms.py,sha256=iaJJV0JFnFKLSXmfrA2Y4qTltkG0HW0sjMPo_c4ON3M,24786
scipy/integrate/__init__.py,sha256=QAHaKXYrkhDhF44xiF9szNiRqLoHmCzC0ePjuEHlJ_U,4346
scipy/integrate/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/__pycache__/_bvp.cpython-311.pyc,,
scipy/integrate/__pycache__/_ode.cpython-311.pyc,,
scipy/integrate/__pycache__/_odepack_py.cpython-311.pyc,,
scipy/integrate/__pycache__/_quad_vec.cpython-311.pyc,,
scipy/integrate/__pycache__/_quadpack_py.cpython-311.pyc,,
scipy/integrate/__pycache__/_quadrature.cpython-311.pyc,,
scipy/integrate/__pycache__/_tanhsinh.cpython-311.pyc,,
scipy/integrate/__pycache__/dop.cpython-311.pyc,,
scipy/integrate/__pycache__/lsoda.cpython-311.pyc,,
scipy/integrate/__pycache__/odepack.cpython-311.pyc,,
scipy/integrate/__pycache__/quadpack.cpython-311.pyc,,
scipy/integrate/__pycache__/vode.cpython-311.pyc,,
scipy/integrate/_bvp.py,sha256=mi8S-exWGnFTlopJti0UDTDnUPjrH6ewrDZYRtJyuzE,42082
scipy/integrate/_dop.cp311-win_amd64.dll.a,sha256=plsGJTUv5uxoR2TFNDaWm89xbNNDWWO9-GYB4rLl_gE,1512
scipy/integrate/_dop.cp311-win_amd64.pyd,sha256=PJlvGaUCV3PMgIzc0vc4cJKaUM-VMGqKfD9H7kn8o-0,433664
scipy/integrate/_ivp/__init__.py,sha256=XVoxnj-1q1Xm4StWijukIoIrqc6Ny1Dxi625GurInWs,264
scipy/integrate/_ivp/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/base.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/bdf.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/common.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/dop853_coefficients.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/ivp.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/lsoda.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/radau.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/rk.cpython-311.pyc,,
scipy/integrate/_ivp/base.py,sha256=efUVvU8k5dFgglCta7f5ZiCMyTU4XN38JMfiKLQcYFQ,10585
scipy/integrate/_ivp/bdf.py,sha256=e5SPzUch5yhXUXMKhoDSRcFVMgrA-7stNvkqagEDkHA,18067
scipy/integrate/_ivp/common.py,sha256=cAISROMGftZFi8f4daQiyZSPfcfJhCcvUNv19sVP2G8,16208
scipy/integrate/_ivp/dop853_coefficients.py,sha256=4oMhmg8eXyzDeSSrlh2dJJMvaabWn-aO44S3wx8Ig5o,7430
scipy/integrate/_ivp/ivp.py,sha256=mPMpFEVNg0vwPKwIQacx_xTuP24n_fw0BU2JwMqZx-k,32220
scipy/integrate/_ivp/lsoda.py,sha256=95NTm0zyHR5zFH5IeKo9kEWtDkdR3Mdfrbubnsf4Awo,10151
scipy/integrate/_ivp/radau.py,sha256=MQp3cjILQqLfNRgRRhQ427OIxCPTi6oxhZ2ldfFjmcU,20336
scipy/integrate/_ivp/rk.py,sha256=F8juhimgqsq5y6-9kpT_BcFqGOhv8PaD__LZcVSonr8,23401
scipy/integrate/_ivp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/_ivp/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_ivp.cpython-311.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_rk.cpython-311.pyc,,
scipy/integrate/_ivp/tests/test_ivp.py,sha256=tRIoV8reB-ncllieVakLSTXGFAEQRFk2aTW4sDrxZPw,42461
scipy/integrate/_ivp/tests/test_rk.py,sha256=_8nQKtotVvR218br-0mNSQSEZ-ST4L2Tgaz5xKXHgJw,1363
scipy/integrate/_lsoda.cp311-win_amd64.dll.a,sha256=Z5bzzKbCcz5LOFqeRI6l173OJPxPrBzhu94bCaGvSA8,1532
scipy/integrate/_lsoda.cp311-win_amd64.pyd,sha256=iO-_AJSKRQ_rhI1quIFpCydRHT0gkIbCWSlqeLK7pU4,553984
scipy/integrate/_ode.py,sha256=asNfUX2GAR7fjbdn-JtWP9htEbFm-x15wa_pYjxgYZ0,49449
scipy/integrate/_odepack.cp311-win_amd64.dll.a,sha256=nIDPN-K734UCt0QKFw02C_onIh2jbEn2DReW5YwGWug,1560
scipy/integrate/_odepack.cp311-win_amd64.pyd,sha256=T6QWiICvMBNeTvU21siMVogQ74TEWWvzSU3l918PXIc,534528
scipy/integrate/_odepack_py.py,sha256=p8g66Ata68is__CWGYICoFuNvdUOe2bYqSQ2kj6oBZI,11416
scipy/integrate/_quad_vec.py,sha256=eVO_-kYSOwjHXaS6IWpwYcYiwyDAwSUhPdl6MZ4yhKk,22182
scipy/integrate/_quadpack.cp311-win_amd64.dll.a,sha256=YUC5p_OqGzdiHxTJkdqKQYNXbuRzqp2lK6c2eTf3byE,1568
scipy/integrate/_quadpack.cp311-win_amd64.pyd,sha256=BTzaWGIIzniNM56JoTiH8YNL9EU7dF7hCfUI1yuMaxA,571904
scipy/integrate/_quadpack_py.py,sha256=07AqrHzMLUl1KW9Nk6xiuXENJ51zr05-e4-qTofUKyA,54531
scipy/integrate/_quadrature.py,sha256=TEhBAjKzFGQXbofag2FOdIwZ0FViurFjQ6bApvTeqJU,60781
scipy/integrate/_tanhsinh.py,sha256=Pf7kV_vrWxB9FySwtgntc0mAWBPMXU5OklrrwxVHRpA,54148
scipy/integrate/_test_multivariate.cp311-win_amd64.dll.a,sha256=2y1I-gRwBZN2zU9wUOkSPDONGcfZpPGmA7YlR9aveas,1676
scipy/integrate/_test_multivariate.cp311-win_amd64.pyd,sha256=_Ku8XfZ7fEgh1GFU5-g_8Hr2KdUFmO_6WmkXHHlR1GM,17920
scipy/integrate/_test_odeint_banded.cp311-win_amd64.dll.a,sha256=gMn6hbjKS6rAmhYxdSqQ56ZtihA_B2Z5NnMmKsuQrHs,1688
scipy/integrate/_test_odeint_banded.cp311-win_amd64.pyd,sha256=AnG7fxMItE9WF9vZBZhgBRn01_FgeQfiMWBPucl5ueU,556032
scipy/integrate/_vode.cp311-win_amd64.dll.a,sha256=xTw-9Nk-HBYB_fjEwBqW1Wv47eCSAiUsORxp5uO826E,1520
scipy/integrate/_vode.cp311-win_amd64.pyd,sha256=4ilbUA8twRSuDyG-9k6hHzuynJSwW8JLAlpvSwU_xEE,615936
scipy/integrate/dop.py,sha256=XFCkBLyUhdNiEb31DGYdiiwYvmkS7FOwUC4HS2RA_g0,437
scipy/integrate/lsoda.py,sha256=KtndEiRbVPejH0aNBaj3mSHWayXP-wqXSoMZJRPWhAg,451
scipy/integrate/odepack.py,sha256=TxsXidD25atNMK5kKXD-FyNCLXnzrwCdVB1hP4aLSQ8,562
scipy/integrate/quadpack.py,sha256=0DHbM39zByxQkBR11xCokuwuJU41YT9bI74hS1pIrXI,627
scipy/integrate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test__quad_vec.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_banded_ode_solvers.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_bvp.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_integrate.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_odeint_jac.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_quadpack.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_quadrature.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_tanhsinh.cpython-311.pyc,,
scipy/integrate/tests/test__quad_vec.py,sha256=Z9y_-oZhglTLpYnP7vrFouxGWWxRf-FIhcdEp9hKdCU,6701
scipy/integrate/tests/test_banded_ode_solvers.py,sha256=erjGeCJNAVCaiulMn698i6ZjMqUFFr0SCZcmo2jJZsM,6905
scipy/integrate/tests/test_bvp.py,sha256=V4jOddeW6ukG3U4jgNinE5hR08ihZ0bZEWOlxe5B6rA,20892
scipy/integrate/tests/test_integrate.py,sha256=AQeUxBCo_LJsPIB7ba0e5GMwc9GLHz7xszGXRJvKW0A,25234
scipy/integrate/tests/test_odeint_jac.py,sha256=XedvNm_tVVuiQhUiL_5_OfPe5nDOEc821vsqiLZtm8c,1890
scipy/integrate/tests/test_quadpack.py,sha256=n37sCEGKxRMVP6jL0y2FfQhLY0QT4A1W_9RoURHlIc8,28746
scipy/integrate/tests/test_quadrature.py,sha256=bdgAAQYOb4H6g-xjLGt6CPnTCBynVuXX0mWsU4e7cu0,28789
scipy/integrate/tests/test_tanhsinh.py,sha256=Ty_VZ6OJFHi3uadfSf552SraS_xmqIvOpgcl1Zo4i74,35337
scipy/integrate/vode.py,sha256=Gkx0nK3_UbOznSvdghjCtUQx4qpSaZzB6rgltKjP6UU,439
scipy/interpolate/__init__.py,sha256=Qo38iQ5KFhyz2V1gj1BjpUFA92Vr_WrZyX3FeiUZsxY,3731
scipy/interpolate/__pycache__/__init__.cpython-311.pyc,,
scipy/interpolate/__pycache__/_bsplines.cpython-311.pyc,,
scipy/interpolate/__pycache__/_cubic.cpython-311.pyc,,
scipy/interpolate/__pycache__/_fitpack2.cpython-311.pyc,,
scipy/interpolate/__pycache__/_fitpack_impl.cpython-311.pyc,,
scipy/interpolate/__pycache__/_fitpack_py.cpython-311.pyc,,
scipy/interpolate/__pycache__/_interpolate.cpython-311.pyc,,
scipy/interpolate/__pycache__/_ndbspline.cpython-311.pyc,,
scipy/interpolate/__pycache__/_ndgriddata.cpython-311.pyc,,
scipy/interpolate/__pycache__/_pade.cpython-311.pyc,,
scipy/interpolate/__pycache__/_polyint.cpython-311.pyc,,
scipy/interpolate/__pycache__/_rbf.cpython-311.pyc,,
scipy/interpolate/__pycache__/_rbfinterp.cpython-311.pyc,,
scipy/interpolate/__pycache__/_rgi.cpython-311.pyc,,
scipy/interpolate/__pycache__/dfitpack.cpython-311.pyc,,
scipy/interpolate/__pycache__/fitpack.cpython-311.pyc,,
scipy/interpolate/__pycache__/fitpack2.cpython-311.pyc,,
scipy/interpolate/__pycache__/interpolate.cpython-311.pyc,,
scipy/interpolate/__pycache__/ndgriddata.cpython-311.pyc,,
scipy/interpolate/__pycache__/polyint.cpython-311.pyc,,
scipy/interpolate/__pycache__/rbf.cpython-311.pyc,,
scipy/interpolate/_bspl.cp311-win_amd64.dll.a,sha256=6x2USXUsegw6eo6kFcLvZ-0g0jW5Rzz-0tJNqjmp2Qs,1520
scipy/interpolate/_bspl.cp311-win_amd64.pyd,sha256=C7_ZdTIKuwx0iZ8TToZG22mrPxlwDc-0Xr6veuio3Xg,569344
scipy/interpolate/_bsplines.py,sha256=O0PRZqrHTdmIXj8hD0sgbm3yMHwFO8gC1c58y0yFzEs,77895
scipy/interpolate/_cubic.py,sha256=C7Bmf7w8KhKWRGwxOzbmr_C5mIbXJWNsytNSj6JEDBE,39601
scipy/interpolate/_dfitpack.cp311-win_amd64.dll.a,sha256=p_n05qDaUPbOklyVome8hWxcjW2GbO-GU63-mJGuUrE,1568
scipy/interpolate/_dfitpack.cp311-win_amd64.pyd,sha256=MSc6UznU6D38frbyDrFe_P6pGPt1jWGpL2nV6Q0Hhqc,663552
scipy/interpolate/_fitpack.cp311-win_amd64.dll.a,sha256=2axE_TzEnG5d2PAmngmdfYZNFbuyoywhdKGmQKxkGGs,1560
scipy/interpolate/_fitpack.cp311-win_amd64.pyd,sha256=fmNnRjisqSwhIaLa-H1dSkQKJK1T9FrT8RzoIO4K6CY,420864
scipy/interpolate/_fitpack2.py,sha256=UPG7wRVgymMkHLxdzRyLFvb6xJY02e8J6u_qtIogza8,91528
scipy/interpolate/_fitpack_impl.py,sha256=knGWj-9gNOIZHVSj0UD6xtQYqtHspEaq9_MlZ0F8sQI,29487
scipy/interpolate/_fitpack_py.py,sha256=1KQpyhyu38mpfq8BpecF7_gcwGToOFD0FZLGTNarhFo,31787
scipy/interpolate/_interpolate.py,sha256=vyHRX4EVvZQG3_7ybnoYCIUGmImkOgcFEbehuieCYJo,81667
scipy/interpolate/_ndbspline.py,sha256=41ELM-4ZXtqMaYb_mfWPvunEFeJrtNp33SsuPZZ3lGs,13100
scipy/interpolate/_ndgriddata.py,sha256=gG8EHsX2cqpEBLoAV-grw7J3gKmysKmYqNcx2BpY9_A,12424
scipy/interpolate/_pade.py,sha256=Phydd4f5oVM_-B3_iwe7H3t66FoCjbfsK_TagWLz9Jo,1894
scipy/interpolate/_polyint.py,sha256=tzpv5wZB16Vb8dwVwXnAlzJfWeU4GYWoNgNQm1__rLY,35921
scipy/interpolate/_ppoly.cp311-win_amd64.dll.a,sha256=uaW7KOxEkmSZZ-utCHrRvNEWTAg9E6h9ks4WJp3Sbi8,1532
scipy/interpolate/_ppoly.cp311-win_amd64.pyd,sha256=tVCeqGTD5VLt2BwQ_6pflEEra8T-uhAqeGMKEv_60NI,441856
scipy/interpolate/_rbf.py,sha256=U7QwA94uCzHjxnFFYOApzqHJ6J6LoncTLs4cJEi_Mng,11964
scipy/interpolate/_rbfinterp.py,sha256=tdgC9U5X78-tCOVFB5ZmCKRs3ouCBqzVaNAUtP9lPbE,20273
scipy/interpolate/_rbfinterp_pythran.cp311-win_amd64.dll.a,sha256=5FrhOuHa3DXSETzjywJ9hfnNQIM6VUCB89z5C47LHOQ,1676
scipy/interpolate/_rbfinterp_pythran.cp311-win_amd64.pyd,sha256=MrZFSKAa-MXw6yvNfcR3Y6lO-74uFH_CFMO5_nFYFIM,1163264
scipy/interpolate/_rgi.py,sha256=bp-PJAC2617HFcDPnm4LRBxkN64paW3-jiTUz51nE08,32257
scipy/interpolate/_rgi_cython.cp311-win_amd64.dll.a,sha256=WGoZBO4pMn88dCLj3nNPtC_Y9SNjuLQyuSM_w9LHm9k,1592
scipy/interpolate/_rgi_cython.cp311-win_amd64.pyd,sha256=GrTUvvy_vUqLtp-tuuBR83u1WPrgu7hk09Fufr-FKe8,273408
scipy/interpolate/dfitpack.py,sha256=d50R9_Wj-obhxOGvFnmmhQP0bVhWgmW6xQNloP_D-XA,959
scipy/interpolate/fitpack.py,sha256=xFDtIx-OMu6QIsF2S3x4nEF7ZzT2c35xGeQBWDJb-Hg,733
scipy/interpolate/fitpack2.py,sha256=52AzGRbO-L23DStEBL_litxtMNILQMWLPx9fmQtyTXo,846
scipy/interpolate/interpnd.cp311-win_amd64.dll.a,sha256=madWg9oz5rM4HBgvMMbPOABvv7ACo5NswoqtQ04t4ik,1560
scipy/interpolate/interpnd.cp311-win_amd64.pyd,sha256=uHPO6xHwiXBoeHHRN1D_RW3KkAqvY_fxDd4CtjRs-l8,411136
scipy/interpolate/interpolate.py,sha256=0MvjiPJfzyQO9_FExxZnTEq-ewR6LjiEtFF6M5RCb_U,784
scipy/interpolate/ndgriddata.py,sha256=QNqLbK1fng-TjM82puP068Stzq-TmFOSkb4mXopUJXg,659
scipy/interpolate/polyint.py,sha256=zfIlcTch3ZLhbT34EqhsfsPOi8h2X8lbnIn6u4EMgQI,696
scipy/interpolate/rbf.py,sha256=Y7ARd1hhej9QRSWR6DuRw4o_s0Tavm5DXYIU5DOdpRM,537
scipy/interpolate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/interpolate/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_bsplines.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack2.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_gil.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_interpnd.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_interpolate.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_ndgriddata.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_pade.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_polyint.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_rbf.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_rbfinterp.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_rgi.cpython-311.pyc,,
scipy/interpolate/tests/data/bug-1310.npz,sha256=jWgDwLOY8nBMI28dG56OXt4GvRZaCrsPIoKBq71FWuk,2648
scipy/interpolate/tests/data/estimate_gradients_hang.npy,sha256=QGwQhXQX_16pjYzSiUXJ0OT1wk-SpIrQ6Pq5Vb8kd_E,35680
scipy/interpolate/tests/data/gcvspl.npz,sha256=A86BVabLoMG_CiRBoQwigZH5Ft7DbLggcjQpgRKWu6g,3138
scipy/interpolate/tests/test_bsplines.py,sha256=NogAM9lven3YqJ7E_bk0JEzAGaFKwqT3gCpgflJsaO4,97088
scipy/interpolate/tests/test_fitpack.py,sha256=1iia5DtpA-CDYpi98-xWSo3rzMlul8JuZTdWbPubMD4,16561
scipy/interpolate/tests/test_fitpack2.py,sha256=BrtNhvSn2XNE1JZlPniBXSWWyFn48tRfbT7aHyogj2U,60081
scipy/interpolate/tests/test_gil.py,sha256=Hehrt38jiqWZIFW9rF8kw1pY-2jwFuikAwuM5RB4GmI,1940
scipy/interpolate/tests/test_interpnd.py,sha256=MfJ3mGi0XOc2ygsdnNO9vwkFygGdy9f1ZcHn_f_yI_A,15848
scipy/interpolate/tests/test_interpolate.py,sha256=cw2uRHxIPyej4SedETaiPtnU8pF1HF1ZPjqhv4vz9E4,97938
scipy/interpolate/tests/test_ndgriddata.py,sha256=G2DrnpFWvHdqy3Hzsd0kwFz35OwstHhmceMboGBkdZ8,11264
scipy/interpolate/tests/test_pade.py,sha256=hyxMyYpWDds6pUN9LaweoZAov32b8dIVrXacdoDOibE,3912
scipy/interpolate/tests/test_polyint.py,sha256=GpS8uDxh7EtJoJF4O5hWN5tcfBLgGbobSxWf4gCl0lQ,37267
scipy/interpolate/tests/test_rbf.py,sha256=p4TlyxPEMCcKaVKbFpl8foncvs8LCyyBrMkYAj2lYfg,6781
scipy/interpolate/tests/test_rbfinterp.py,sha256=YyAtQOl6qPrsgOrQ_3raS20ra4B9UA4M4LVpVDj1fuk,19034
scipy/interpolate/tests/test_rgi.py,sha256=v5q43NcnejLco1w5X7kNcZwhT5ozy9QMp-oQ7YmGdGM,45978
scipy/io/__init__.py,sha256=3ETOeDKr2QQL-Ty2qrrzxAzqX09sfJzsKaPm5r8tZyA,2851
scipy/io/__pycache__/__init__.cpython-311.pyc,,
scipy/io/__pycache__/_fortran.cpython-311.pyc,,
scipy/io/__pycache__/_idl.cpython-311.pyc,,
scipy/io/__pycache__/_mmio.cpython-311.pyc,,
scipy/io/__pycache__/_netcdf.cpython-311.pyc,,
scipy/io/__pycache__/harwell_boeing.cpython-311.pyc,,
scipy/io/__pycache__/idl.cpython-311.pyc,,
scipy/io/__pycache__/mmio.cpython-311.pyc,,
scipy/io/__pycache__/netcdf.cpython-311.pyc,,
scipy/io/__pycache__/wavfile.cpython-311.pyc,,
scipy/io/_fast_matrix_market/__init__.py,sha256=FSHlNhBhsutw1-vFVdG2Zxk3500Zm6IFEp1555w21m4,17470
scipy/io/_fast_matrix_market/__pycache__/__init__.cpython-311.pyc,,
scipy/io/_fast_matrix_market/_fmm_core.cp311-win_amd64.dll.a,sha256=vHhJsx9w6uF1ZeRGmlgwSf94jCCEbp1A_84uLSVs4VM,1568
scipy/io/_fast_matrix_market/_fmm_core.cp311-win_amd64.pyd,sha256=Ny8GVJL9RUm7riFEVUiNrpeOGK2u4rxp6Z_Q9do4t9M,2749440
scipy/io/_fortran.py,sha256=vaaYYQPTMA8TiFob56TKborkigS6wnuZl2xGnpJO9ws,11249
scipy/io/_harwell_boeing/__init__.py,sha256=bPKU-59CohUohubhJBpDFqtFbCo3v3Ptl9hb-FHaFi8,171
scipy/io/_harwell_boeing/__pycache__/__init__.cpython-311.pyc,,
scipy/io/_harwell_boeing/__pycache__/_fortran_format_parser.cpython-311.pyc,,
scipy/io/_harwell_boeing/__pycache__/hb.cpython-311.pyc,,
scipy/io/_harwell_boeing/_fortran_format_parser.py,sha256=1bJDeg-occK-jF_eneTCRc6nj9yWQU1QGPweRNMkflw,9226
scipy/io/_harwell_boeing/hb.py,sha256=fQuXcJapdo4u6sNjFFNFwWaAas0kSlg46L91PGt0Lwk,19957
scipy/io/_harwell_boeing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/_harwell_boeing/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_fortran_format.cpython-311.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_hb.cpython-311.pyc,,
scipy/io/_harwell_boeing/tests/test_fortran_format.py,sha256=E3uHrWQdUXjdjbNpkYUZ516Ug4nPjWucK8fJ9ZBt77I,2457
scipy/io/_harwell_boeing/tests/test_hb.py,sha256=BCfVyKAE3O0ABcfeGfxtGquwhehBGMPKGJl95oC3EIo,2349
scipy/io/_idl.py,sha256=Ym0tUIyqMhZkx9Sr7_A9HTWXXIHVD1D61FVAFbsnMPY,28020
scipy/io/_mmio.py,sha256=_V9VYFI084fqhAo0bbBN9FtrVfyLVeGR2dqwOeh_lkc,32833
scipy/io/_netcdf.py,sha256=kM4993iMVHNPtqJ8X1W1HOPERpsPzzu9xPX2zqW30ow,40358
scipy/io/_test_fortran.cp311-win_amd64.dll.a,sha256=7FyP4o7jCDtanViWER4oIRX5WYH6HbcQLrzjxZa6BJo,1616
scipy/io/_test_fortran.cp311-win_amd64.pyd,sha256=WHA4fP_Laq7mYOZDsH0VSrRqFBDDLSu9k4oSDb7zmAE,381440
scipy/io/arff/__init__.py,sha256=3F0yvqizJT7Hq5gygpmZw2WMuy0r9aCmb6G2yNNn8-I,833
scipy/io/arff/__pycache__/__init__.cpython-311.pyc,,
scipy/io/arff/__pycache__/_arffread.cpython-311.pyc,,
scipy/io/arff/__pycache__/arffread.cpython-311.pyc,,
scipy/io/arff/_arffread.py,sha256=KAVr3DpekgNZXXM7zJfDyUT02kxGK5xBeLIKRxpgnjI,27467
scipy/io/arff/arffread.py,sha256=RMsdm5rayUdCfdr9psq85Bb15NxLk0z8A6nBeaCopNw,594
scipy/io/arff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/arff/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/io/arff/tests/__pycache__/test_arffread.cpython-311.pyc,,
scipy/io/arff/tests/data/iris.arff,sha256=gzevjIJmmJLocLjOEnQMkZVZuIpfPJLocJdIG4RYHIk,7711
scipy/io/arff/tests/data/missing.arff,sha256=m53l1Himyx0TtVgNLucoz1go8-9BmSE70Ta9bm6dt_k,128
scipy/io/arff/tests/data/nodata.arff,sha256=6oAHQ7CUQEG8h5glXKpCaXH0UABOCEoxs5j50WYPAKU,240
scipy/io/arff/tests/data/quoted_nominal.arff,sha256=rc8IS9thME1NM9tAR-C4IUixLrSpw64YNeHkIRMS6Ng,299
scipy/io/arff/tests/data/quoted_nominal_spaces.arff,sha256=NbE6wwE0HkJDomErLPHgPGO6iF2HA1bLpmcamzx3F58,305
scipy/io/arff/tests/data/test1.arff,sha256=NV86lzIKWCwq3UNZmMGt1XK0yurcYmcp0KS9c5rKLkE,201
scipy/io/arff/tests/data/test10.arff,sha256=GbDWnG6LzjsbjYxiarYG4Ft3WlK1rvuM6w3proNFD58,199016
scipy/io/arff/tests/data/test11.arff,sha256=RFWNoWrx2LHJ0izc1bJdmGA2M2L8NT58MwKQN2B-3AI,252
scipy/io/arff/tests/data/test2.arff,sha256=zk5hNFTEcphFXkon2At_FD9ia_lAyzOcSFj4xnJTVPs,315
scipy/io/arff/tests/data/test3.arff,sha256=R2zwCzapweGqylmLD-LXx7GAS957NNJ8emM48hLTwEY,78
scipy/io/arff/tests/data/test4.arff,sha256=KtyokEnupbYZVi-fR7GJtr-hHefP54xgV2l26Nz3YZU,249
scipy/io/arff/tests/data/test5.arff,sha256=_1-QhpmflJKp1polaKwjkeJZsHeho7RzM3_ic2JJykA,391
scipy/io/arff/tests/data/test6.arff,sha256=WOCPN8QRdxCUSfzs-Y-OlJKiUmgLKuDrfy6wsWIFCJU,207
scipy/io/arff/tests/data/test7.arff,sha256=AQdPXYEKEhb10Odd_SDrPcv_gHNR0ymFKnD_91zjFc8,573
scipy/io/arff/tests/data/test8.arff,sha256=YlCbztVn-7x4w41HLIP852BqkKvWBrO9N8Nb0LSR0ZM,440
scipy/io/arff/tests/data/test9.arff,sha256=X0fH-_mz5ScbEvPJV7LJH-7Kbr2RTOlamf2_x18ydY0,324
scipy/io/arff/tests/test_arffread.py,sha256=YFtoh4I83UWxVdMBhLhGIkRRG1_U01e3EzKFcDZyzFw,13525
scipy/io/harwell_boeing.py,sha256=uZ7NZBLRwQ39GlELGYby_JsIHjMDp1NGbc7cjWq6OFs,555
scipy/io/idl.py,sha256=FgICucFl-tR94QRdtx099cFD8YEbGJ_fH9A1cT5qRGg,521
scipy/io/matlab/__init__.py,sha256=7N61Ssi3RyZbcXWAX5SHdfCsDkj3TIy1cHQkdC6csQY,2091
scipy/io/matlab/__pycache__/__init__.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_byteordercodes.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_mio.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_mio4.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_mio5.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_mio5_params.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_miobase.cpython-311.pyc,,
scipy/io/matlab/__pycache__/byteordercodes.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio4.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio5.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio5_params.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio5_utils.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio_utils.cpython-311.pyc,,
scipy/io/matlab/__pycache__/miobase.cpython-311.pyc,,
scipy/io/matlab/__pycache__/streams.cpython-311.pyc,,
scipy/io/matlab/_byteordercodes.py,sha256=tVJ4_1yhVsey9Yaabn9mjl7srPkkdEWJ5YjRP3LLapw,2060
scipy/io/matlab/_mio.py,sha256=jOzuJA9ZEy3bHUL5yFjQSn0rXpJoNmUcMlRvXPiFNno,13170
scipy/io/matlab/_mio4.py,sha256=gOKQZBs3FtAYcbVvTU3_seyvGHagO58R3GdkyLDGooY,21352
scipy/io/matlab/_mio5.py,sha256=nE265QpuyvmdO-K0-v1xYEqCQAd_996l3FMA8HHktuo,34509
scipy/io/matlab/_mio5_params.py,sha256=f5HyjzbAwkN4zNRwn0N_n01LadTbsmUFGWjflumbxeU,8482
scipy/io/matlab/_mio5_utils.cp311-win_amd64.dll.a,sha256=9Ozd2mIfkohF3qKiyjK-W82wDzheayVtyEAGxyJkzEI,1592
scipy/io/matlab/_mio5_utils.cp311-win_amd64.pyd,sha256=WquEIJClBZuqdcKQ4KQEgYfMAUxCrCigmDdlWhGMZEg,210432
scipy/io/matlab/_mio_utils.cp311-win_amd64.dll.a,sha256=I6u01qwJlvEU-1W0DZ0cl6eo5pMlQDnB8ZfItf7lD60,1580
scipy/io/matlab/_mio_utils.cp311-win_amd64.pyd,sha256=WN2T-omHQ5zPFQ6LksEL4prCI9Vyih3XAulNMTYMTbw,58368
scipy/io/matlab/_miobase.py,sha256=wo_8RtYGeAJTVQ8XGrvYDk10GJB-oRdt7f6v1CkPSIQ,13212
scipy/io/matlab/_streams.cp311-win_amd64.dll.a,sha256=MUO2rGmmjhtgZ9cxjF9day3L-9s7Dc63r2kDTHLef3U,1560
scipy/io/matlab/_streams.cp311-win_amd64.pyd,sha256=UphTPt4eGFG0DnsreuQgUmOM55XduobUZSSOXqdYWc4,113664
scipy/io/matlab/byteordercodes.py,sha256=iyqwM-3YIUCH9LJdOEbvMJ7n2gu6nxKdyb0zspXeyA4,545
scipy/io/matlab/mio.py,sha256=Rd-5E8FJ__4ylQfqBLsiZXuHdOs_kpqW1gu6U8uuEjA,555
scipy/io/matlab/mio4.py,sha256=-r3TPyoOQL2JFpfg_Tkkq1TLGVwU5puFeSfpDfP2_qc,525
scipy/io/matlab/mio5.py,sha256=LDYpi8yS5Sh_1n-U665ILxjhv2k4aEdepb4jv6pdIUc,657
scipy/io/matlab/mio5_params.py,sha256=k_DuRuxmwMBXDdKrrpa2gDmU0ZLudzxyKJhmiDZ32fg,611
scipy/io/matlab/mio5_utils.py,sha256=zAGGUPRweBSnK2VclfLePonc3N63WwH7q4rta4VFBzM,537
scipy/io/matlab/mio_utils.py,sha256=3YjORgt2MmmZvxXLnk680YX-3cs7YVvKxRyMqUfHo4M,535
scipy/io/matlab/miobase.py,sha256=ucyFrqpuWm2GNepC3PB9Zg0xs2bGVqvYPscphlxVHbQ,581
scipy/io/matlab/streams.py,sha256=QjbFVhqfk3IR1ow5SPpE37Be47jJr5169sNTC1hhpmY,529
scipy/io/matlab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/matlab/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_byteordercodes.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio5_utils.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_funcs.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_utils.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_miobase.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_pathological.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_streams.cpython-311.pyc,,
scipy/io/matlab/tests/data/bad_miuint32.mat,sha256=CVkYHp_U4jxYKRRHSuZ5fREop4tJjnZcQ02DKfObkRA,272
scipy/io/matlab/tests/data/bad_miutf8_array_name.mat,sha256=V-jfVMkYyy8qRGcOIsNGcoO0GCgTxchrsQUBGBnfWHE,208
scipy/io/matlab/tests/data/big_endian.mat,sha256=2ttpiaH2B6nmHnq-gsFeMvZ2ZSLOlpzt0IJiqBTcc8M,273
scipy/io/matlab/tests/data/broken_utf8.mat,sha256=nm8aotRl6NIxlM3IgPegKR3EeevYZoJCrYpV4Sa1T5I,216
scipy/io/matlab/tests/data/corrupted_zlib_checksum.mat,sha256=X4dvE7K9DmGEF3D6I-48hC86W41jB54H7bD8KTXjtYA,276
scipy/io/matlab/tests/data/corrupted_zlib_data.mat,sha256=DfE1YBH-pYw-dAaEeKA6wZcyKeo9GlEfrzZtql-fO_w,3451
scipy/io/matlab/tests/data/japanese_utf8.txt,sha256=rgxiBH7xmEKF91ZkB3oMLrqABBXINEMHPXDKdZXNBEY,270
scipy/io/matlab/tests/data/little_endian.mat,sha256=FQP_2MNod-FFF-JefN7ZxovQ6QLCdHQ0DPL_qBCP44Y,265
scipy/io/matlab/tests/data/logical_sparse.mat,sha256=qujUUpYewaNsFKAwGpYS05z7kdUv9TQZTHV5_lWhRrs,208
scipy/io/matlab/tests/data/malformed1.mat,sha256=DTuTr1-IzpLMBf8u5DPb3HXmw9xJo1aWfayA5S_3zUI,2208
scipy/io/matlab/tests/data/miuint32_for_miint32.mat,sha256=romrBP_BS46Sl2-pKWsUnxYDad2wehyjq4wwLaVqums,272
scipy/io/matlab/tests/data/miutf8_array_name.mat,sha256=Vo8JptFr-Kg2f2cEoDg8LtELSjVNyccdJY74WP_kqtc,208
scipy/io/matlab/tests/data/nasty_duplicate_fieldnames.mat,sha256=bvdmj6zDDUIpOfIP8J4Klo107RYCDd5VK5gtOYx3GsU,8168
scipy/io/matlab/tests/data/one_by_zero_char.mat,sha256=Z3QdZjTlOojjUpS0cfBP4XfNQI3GTjqU0n_pnAzgQhU,184
scipy/io/matlab/tests/data/parabola.mat,sha256=ENWuWX_uwo4Av16dIGOwnbMReAMrShDhalkq8QUI8Rg,729
scipy/io/matlab/tests/data/single_empty_string.mat,sha256=4uTmX0oydTjmtnhxqi9SyPWCG2I24gj_5LarS80bPik,171
scipy/io/matlab/tests/data/some_functions.mat,sha256=JA736oG3s8PPdKhdsYK-BndLUsGrJCJAIRBseSIEZtM,1397
scipy/io/matlab/tests/data/sqr.mat,sha256=3DtGl_V4wABKCDQ0P3He5qfOzpUTC-mINdK73MKS7AM,679
scipy/io/matlab/tests/data/test3dmatrix_6.1_SOL2.mat,sha256=-odiBIQAbOLERg0Vg682QHGfs7C8MaA_gY77OWR8x78,232
scipy/io/matlab/tests/data/test3dmatrix_6.5.1_GLNX86.mat,sha256=G5siwvZ-7Uv5KJ6h7AA3OHL6eiFsd8Lnjx4IcoByzCU,232
scipy/io/matlab/tests/data/test3dmatrix_7.1_GLNX86.mat,sha256=EVj1wPnoyWGIdTpkSj3YAwqzTAm27eqZNxCaJAs3pwU,213
scipy/io/matlab/tests/data/test3dmatrix_7.4_GLNX86.mat,sha256=S_Sd3sxorDd8tZ5CxD5_J8vXbfcksLWzhUQY5b82L9g,213
scipy/io/matlab/tests/data/test_empty_struct.mat,sha256=WoC7g7TyXqNr2T0d5xE3IUq5PRzatE0mxXjqoHX5Xec,173
scipy/io/matlab/tests/data/test_mat4_le_floats.mat,sha256=2xvn3Cg4039shJl62T-bH-VeVP_bKtwdqvGfIxv8FJ4,38
scipy/io/matlab/tests/data/test_skip_variable.mat,sha256=pJLVpdrdEb-9SMZxaDu-uryShlIi90l5LfXhvpVipJ0,20225
scipy/io/matlab/tests/data/testbool_8_WIN64.mat,sha256=_xBw_2oZA7u9Xs6GJItUpSIEV4jVdfdcwzmLNFWM6ow,185
scipy/io/matlab/tests/data/testcell_6.1_SOL2.mat,sha256=OWOBzNpWTyAHIcZABRytVMcABiRYgEoMyF9gDaIkFe4,536
scipy/io/matlab/tests/data/testcell_6.5.1_GLNX86.mat,sha256=7111TN_sh1uMHmYx-bjd_v9uaAnWhJMhrQFAtAw6Nvk,536
scipy/io/matlab/tests/data/testcell_7.1_GLNX86.mat,sha256=62p6LRW6PbM-Y16aUeGVhclTVqS5IxPUtsohe7MjrYo,283
scipy/io/matlab/tests/data/testcell_7.4_GLNX86.mat,sha256=NkTA8UW98hIQ0t5hGx_leG-MzNroDelYwqx8MPnO63Q,283
scipy/io/matlab/tests/data/testcellnest_6.1_SOL2.mat,sha256=AeNaog8HUDCVrIuGICAXYu9SGDsvV6qeGjgvWHrVQho,568
scipy/io/matlab/tests/data/testcellnest_6.5.1_GLNX86.mat,sha256=Gl4QA0yYwGxjiajjgWS939WVAM-W2ahNIm9wwMaT5oc,568
scipy/io/matlab/tests/data/testcellnest_7.1_GLNX86.mat,sha256=CUGtkwIU9CBa0Slx13mbaM67_ec0p-unZdu8Z4YYM3c,228
scipy/io/matlab/tests/data/testcellnest_7.4_GLNX86.mat,sha256=TeTk5yjl5j_bcnmIkpzuYHxGGQXNu-rK6xOsN4t6lX8,228
scipy/io/matlab/tests/data/testcomplex_4.2c_SOL2.mat,sha256=WOwauWInSVUFBuOJ1Bo3spmUQ3UWUIlsIe4tYGlrU7o,176
scipy/io/matlab/tests/data/testcomplex_6.1_SOL2.mat,sha256=GpAEccizI8WvlrBPdvlKUv6uKbZOo_cjUK3WVVb2lo4,352
scipy/io/matlab/tests/data/testcomplex_6.5.1_GLNX86.mat,sha256=3MEbf0zJdQGAO7x-pzFCup2QptfYJHQG59z0vVOdxl4,352
scipy/io/matlab/tests/data/testcomplex_7.1_GLNX86.mat,sha256=VNHV2AIEkvPuhae1kKIqt5t8AMgUyr0L_CAp-ykLxt4,247
scipy/io/matlab/tests/data/testcomplex_7.4_GLNX86.mat,sha256=8rWGf5bqY7_2mcd5w5gTYgMkXVePlLL8qT7lh8kApn0,247
scipy/io/matlab/tests/data/testdouble_4.2c_SOL2.mat,sha256=MzT7OYPEUXHYNPBrVkyKEaG5Cas2aOA0xvrO7l4YTrQ,103
scipy/io/matlab/tests/data/testdouble_6.1_SOL2.mat,sha256=DpB-mVKx1gsjl-3IbxfxHNuzU5dnuku-MDQCA8kALVI,272
scipy/io/matlab/tests/data/testdouble_6.5.1_GLNX86.mat,sha256=4hY5VEubavNEv5KvcqQnd7MWWvFUzHXXpYIqUuUt-50,272
scipy/io/matlab/tests/data/testdouble_7.1_GLNX86.mat,sha256=N2QOOIXPyy0zPZZ_qY7xIDaodMGrTq3oXNBEHZEscw0,232
scipy/io/matlab/tests/data/testdouble_7.4_GLNX86.mat,sha256=TrkJ4Xx_dC9YrPdewlsOvYs_xag7gT3cN4HkDsJmT8I,232
scipy/io/matlab/tests/data/testemptycell_5.3_SOL2.mat,sha256=g96Vh9FpNhkiWKsRm4U6KqeKd1hNAEyYSD7IVzdzwsU,472
scipy/io/matlab/tests/data/testemptycell_6.5.1_GLNX86.mat,sha256=2Zw-cMv-Mjbs2HkSl0ubmh_htFUEpkn7XVHG8iM32o0,472
scipy/io/matlab/tests/data/testemptycell_7.1_GLNX86.mat,sha256=t5Ar8EgjZ7fkTUHIVpdXg-yYWo_MBaigMDJUGWEIrmU,218
scipy/io/matlab/tests/data/testemptycell_7.4_GLNX86.mat,sha256=5PPvfOoL-_Q5ou_2nIzIrHgeaOZGFXGxAFdYzCQuwEQ,218
scipy/io/matlab/tests/data/testfunc_7.4_GLNX86.mat,sha256=ScTKftENe78imbMc0I5ouBlIMcEEmZgu8HVKWAMNr58,381
scipy/io/matlab/tests/data/testhdf5_7.4_GLNX86.mat,sha256=ZoVbGk38_MCppZ0LRr6OE07HL8ZB4rHXgMj9LwUBgGg,4168
scipy/io/matlab/tests/data/testmatrix_4.2c_SOL2.mat,sha256=14YMiKAN9JCPTqSDXxa58BK6Un7EM4hEoSGAUuwKWGQ,151
scipy/io/matlab/tests/data/testmatrix_6.1_SOL2.mat,sha256=ZdjNbcIE75V5Aht5EVBvJX26aabvNqbUH0Q9VBnxBS4,216
scipy/io/matlab/tests/data/testmatrix_6.5.1_GLNX86.mat,sha256=OB82QgB6SwtsxT4t453OVSj-B777XrHGEGOMgMD1XGc,216
scipy/io/matlab/tests/data/testmatrix_7.1_GLNX86.mat,sha256=-TYB0kREY7i7gt5x15fOYjXi410pXuDWUFxPYuMwywI,193
scipy/io/matlab/tests/data/testmatrix_7.4_GLNX86.mat,sha256=l9psDc5K1bpxNeuFlyYIYauswLnOB6dTX6-jvelW0kU,193
scipy/io/matlab/tests/data/testminus_4.2c_SOL2.mat,sha256=2914WYQajPc9-Guy3jDOLU3YkuE4OXC_63FUSDzJzX0,38
scipy/io/matlab/tests/data/testminus_6.1_SOL2.mat,sha256=2X2fZKomz0ktBvibj7jvHbEvt2HRA8D6hN9qA1IDicw,200
scipy/io/matlab/tests/data/testminus_6.5.1_GLNX86.mat,sha256=i364SgUCLSYRjQsyygvY1ArjEaO5uLip3HyU-R7zaLo,200
scipy/io/matlab/tests/data/testminus_7.1_GLNX86.mat,sha256=gtYNC9_TciYdq8X9IwyGEjiw2f1uCVTGgiOPFOiQbJc,184
scipy/io/matlab/tests/data/testminus_7.4_GLNX86.mat,sha256=eXcoTM8vKuh4tQnl92lwdDaqssGB6G9boSHh3FOCkng,184
scipy/io/matlab/tests/data/testmulti_4.2c_SOL2.mat,sha256=Zhyu2KCsseSJ5NARdS00uwddCs4wmjcWNP2LJFns2-Q,240
scipy/io/matlab/tests/data/testmulti_7.1_GLNX86.mat,sha256=KI3H58BVj6k6MFsj8icSbjy_0Z-jOesWN5cafStLPG8,276
scipy/io/matlab/tests/data/testmulti_7.4_GLNX86.mat,sha256=Yr4YKCP27yMWlK5UOK3BAEOAyMr-m0yYGcj8v1tCx-I,276
scipy/io/matlab/tests/data/testobject_6.1_SOL2.mat,sha256=kzLxy_1o1HclPXWyA-SX5gl6LsG1ioHuN4eS6x5iZio,800
scipy/io/matlab/tests/data/testobject_6.5.1_GLNX86.mat,sha256=dq_6_n0v7cUz9YziXn-gZFNc9xYtNxZ8exTsziWIM7s,672
scipy/io/matlab/tests/data/testobject_7.1_GLNX86.mat,sha256=3z-boFw0SC5142YPOLo2JqdusPItVzjCFMhXAQNaQUQ,306
scipy/io/matlab/tests/data/testobject_7.4_GLNX86.mat,sha256=5OwLTMgCBlxsDfiEUzlVjqcSbVQG-X5mIw5JfW3wQXA,306
scipy/io/matlab/tests/data/testonechar_4.2c_SOL2.mat,sha256=BCvppGhO19-j-vxAvbdsORIiyuJqzCuQog9Ao8V1lvA,40
scipy/io/matlab/tests/data/testonechar_6.1_SOL2.mat,sha256=ThppTHGJFrUfal5tewS70DL00dSwk1otazuVdJrTioE,200
scipy/io/matlab/tests/data/testonechar_6.5.1_GLNX86.mat,sha256=SBfN6e7Vz1rAdi8HLguYXcHUHk1viaXTYccdEyhhob4,200
scipy/io/matlab/tests/data/testonechar_7.1_GLNX86.mat,sha256=m8W9GqvflfAsizkhgAfT0lLcxuegZIWCLNuHVX69Jac,184
scipy/io/matlab/tests/data/testonechar_7.4_GLNX86.mat,sha256=t9ObKZOLy3vufnER8TlvQcUkd_wmXbJSdQoG4f3rVKY,184
scipy/io/matlab/tests/data/testscalarcell_7.4_GLNX86.mat,sha256=5LX9sLH7Y6h_N_a1XRN2GuMgp_P7ECpPsXGDOypAJg0,194
scipy/io/matlab/tests/data/testsimplecell.mat,sha256=Aoeh0PX2yiLDTwkxMEyZ_CNX2mJHZvyfuFJl817pA1c,220
scipy/io/matlab/tests/data/testsparse_4.2c_SOL2.mat,sha256=dFUcB1gunfWqexgR4YDZ_Ec0w0HffM1DUE1C5PVfDDc,223
scipy/io/matlab/tests/data/testsparse_6.1_SOL2.mat,sha256=9Sgd_SPkGNim7ZL0xgD71qml3DK0yDHYC7VSNLNQEXA,280
scipy/io/matlab/tests/data/testsparse_6.5.1_GLNX86.mat,sha256=jp1ILNxLyV6XmCCGxAz529XoZ9dhCqGEO-ExPH70_Pg,328
scipy/io/matlab/tests/data/testsparse_7.1_GLNX86.mat,sha256=k8QuQ_4Zu7FWTzHjRnHCVZ9Yu5vwNP0WyNzu6TuiY-4,229
scipy/io/matlab/tests/data/testsparse_7.4_GLNX86.mat,sha256=QbZOCqIvnaK0XOH3kaSXBe-m_1_Rb33psq8E-WMSBTU,229
scipy/io/matlab/tests/data/testsparsecomplex_4.2c_SOL2.mat,sha256=QMVoBXVyl9RBGvAjLoiW85kAXYJ-hHprUMegEG69A5w,294
scipy/io/matlab/tests/data/testsparsecomplex_6.1_SOL2.mat,sha256=WfEroAT5YF4HGAKq3jTJxlFrKaTCh3rwlSlKu__VjwA,304
scipy/io/matlab/tests/data/testsparsecomplex_6.5.1_GLNX86.mat,sha256=e0s6cyoKJeYMArdceHpnKDvtCVcw7XuB44OBDHpoa6U,400
scipy/io/matlab/tests/data/testsparsecomplex_7.1_GLNX86.mat,sha256=kgHcuq-deI2y8hfkGwlMOkW7lntexdPHfuz0ar6b3jo,241
scipy/io/matlab/tests/data/testsparsecomplex_7.4_GLNX86.mat,sha256=rYCaWNLXK7f_jjMc6_UvZz6ZDuMCuVRmJV5RyeXiDm8,241
scipy/io/matlab/tests/data/testsparsefloat_7.4_GLNX86.mat,sha256=hnNV6GZazEeqTXuA9vcOUo4xam_UnKRYGYH9PUGTLv8,219
scipy/io/matlab/tests/data/teststring_4.2c_SOL2.mat,sha256=cAhec51DlqIYfDXXGaumOE3Hqb3cFWM1UsUK3K_lDP8,375
scipy/io/matlab/tests/data/teststring_6.1_SOL2.mat,sha256=ciFzNGMO7gjYecony-E8vtOwBY4vXIUhyug6Euaz3Kg,288
scipy/io/matlab/tests/data/teststring_6.5.1_GLNX86.mat,sha256=yrJrpLiwLvU_LI1D6rw1Pk1qJK1YlC7Cmw7lwyJVLtw,288
scipy/io/matlab/tests/data/teststring_7.1_GLNX86.mat,sha256=zo7sh-8dMpGqhoNxLEnfz3Oc7RonxiY5j0B3lxk0e8o,224
scipy/io/matlab/tests/data/teststring_7.4_GLNX86.mat,sha256=igL_CvtAcNEa1nxunDjQZY5wS0rJOlzsUkBiDreJssk,224
scipy/io/matlab/tests/data/teststringarray_4.2c_SOL2.mat,sha256=pRldk-R0ig1k3ouvaR9oVtBwZsQcDW_b4RBEDYu1-Vk,156
scipy/io/matlab/tests/data/teststringarray_6.1_SOL2.mat,sha256=B9IdaSsyb0wxjyYyHOj_GDO0laAeWDEJhoEhC9xdm1E,232
scipy/io/matlab/tests/data/teststringarray_6.5.1_GLNX86.mat,sha256=t4tKGJg2NEg_Ar5MkOjCoQb2hVL8Q_Jdh9FF4TPL_4g,232
scipy/io/matlab/tests/data/teststringarray_7.1_GLNX86.mat,sha256=lpYkBZX8K-c4FO5z0P9DMfYc7Y-yzyg11J6m-19uYTU,203
scipy/io/matlab/tests/data/teststringarray_7.4_GLNX86.mat,sha256=lG-c7U-5Bo8j8xZLpd0JAsMYwewT6cAw4eJCZH5xf6E,203
scipy/io/matlab/tests/data/teststruct_6.1_SOL2.mat,sha256=3GJbA4O7LP57J6IYzmJqTPeSJrEaiNSk-rg7h0ANR1w,608
scipy/io/matlab/tests/data/teststruct_6.5.1_GLNX86.mat,sha256=fRbqAnzTeOU3dTQx7O24MfMVFr6pM5u594FRrPPkYJE,552
scipy/io/matlab/tests/data/teststruct_7.1_GLNX86.mat,sha256=mCtI_Yot08NazvWHvehOZbTV4bW_I4-D5jBgJ6T9EbI,314
scipy/io/matlab/tests/data/teststruct_7.4_GLNX86.mat,sha256=52qaF4HRCtPl1jE6ljbkEl2mofZVAPpmBxrm-J5OTTI,314
scipy/io/matlab/tests/data/teststructarr_6.1_SOL2.mat,sha256=vneCpWBwApBGfeKzdZcybyajxjR-ZYf64j0l08_hU84,528
scipy/io/matlab/tests/data/teststructarr_6.5.1_GLNX86.mat,sha256=gqhRpSfNNB5SR9sCp-wWrvokr5VV_heGnvco6dmfOvY,472
scipy/io/matlab/tests/data/teststructarr_7.1_GLNX86.mat,sha256=6VDU0mtTBEG0bBHqKP1p8xq846eMhSZ_WvBZv8MzE7M,246
scipy/io/matlab/tests/data/teststructarr_7.4_GLNX86.mat,sha256=ejtyxeeX_W1a2rNrEUUiG9txPW8_UtSgt8IaDOxE2pg,246
scipy/io/matlab/tests/data/teststructnest_6.1_SOL2.mat,sha256=sbi0wUwOrbU-gBq3lyDwhAbvchdtOJkflOR_MU7uGKA,496
scipy/io/matlab/tests/data/teststructnest_6.5.1_GLNX86.mat,sha256=uTkKtrYBTuz4kICVisEaG7V5C2nJDKjy92mPDswTLPE,416
scipy/io/matlab/tests/data/teststructnest_7.1_GLNX86.mat,sha256=o4F2jOhYyNpJCo-BMg6v_ITZQvjenXfXHLq94e7iwRo,252
scipy/io/matlab/tests/data/teststructnest_7.4_GLNX86.mat,sha256=CNXO12O6tedEuMG0jNma4qfbTgCswAbHwh49a3uE3Yk,252
scipy/io/matlab/tests/data/testunicode_7.1_GLNX86.mat,sha256=KV97FCW-1XZiXrwXJoZPbgyAht79oIFHa917W1KFLwE,357
scipy/io/matlab/tests/data/testunicode_7.4_GLNX86.mat,sha256=9-8xzACZleBkMjZnbr8t4Ncs9B6mbzrONDblPnteBPU,357
scipy/io/matlab/tests/data/testvec_4_GLNX86.mat,sha256=GQzR3mBVS266_NBfrRC9X0dLgmeu8Jl4r4ZYMOrn1V0,93
scipy/io/matlab/tests/test_byteordercodes.py,sha256=YKPAsE36MRPDi3Jsh2QrinvLOEvDPmsBylkwUzqoDTg,967
scipy/io/matlab/tests/test_mio.py,sha256=t6CenLDZSnvBsvwT3y0-4hanNpBJOoPOlY2Wh446Ia0,46149
scipy/io/matlab/tests/test_mio5_utils.py,sha256=Z1Byr0AJMkdlidOETqIsAJdCZCx0TGqR4OyFI4SDBaY,5568
scipy/io/matlab/tests/test_mio_funcs.py,sha256=JoLN42aZLd-f-kju-kTgRFQjQmEy4VtJlKgJ5EgnVFI,1443
scipy/io/matlab/tests/test_mio_utils.py,sha256=I4_z8My_JDy6jverX4bm-GeDNnnC6jX71hprsGSnCF8,1639
scipy/io/matlab/tests/test_miobase.py,sha256=G3Xm9Q14vWR37Wt7uxxgIBnLvmSkJ86GVABDhlJMflU,1496
scipy/io/matlab/tests/test_pathological.py,sha256=8lcveXzzknQH_009kNTvrf4aAr-qgatrXPEuRZtxQ4w,1088
scipy/io/matlab/tests/test_streams.py,sha256=3T0aC_eSvutWlfNsKOL4JCUUqg9jYEhGbPgJ4ZdynpM,7638
scipy/io/mmio.py,sha256=qDHalmZoj_VZLTcaO65zAXs-1ZF69aeKoF9p1R-Mh1w,543
scipy/io/netcdf.py,sha256=YaRtHSM0jdaFB5W-Og7vU6CsgaVhEOaogTwObhuHTOA,550
scipy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_fortran.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_idl.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_mmio.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_netcdf.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_paths.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_wavfile.cpython-311.pyc,,
scipy/io/tests/data/Transparent Busy.ani,sha256=vwoK3ysYo87-TwzvjerHjFjSPIGpw83jjiMDXcHPWjA,4362
scipy/io/tests/data/array_float32_1d.sav,sha256=A_xXWkfS1sQCxP4ONezeEZvlKEXwZ1TPG2rCCFdmBNM,2628
scipy/io/tests/data/array_float32_2d.sav,sha256=qJmN94pywXznXMHzt-L6DJgaIq_FfruVKJl_LMaI8UU,3192
scipy/io/tests/data/array_float32_3d.sav,sha256=U7P6As7Nw6LdBY1pTOaW9C-O_NlXLXZwSgbT3H8Z8uk,13752
scipy/io/tests/data/array_float32_4d.sav,sha256=Tl6erEw_Zq3dwVbVyPXRWqB83u_o4wkIVFOe3wQrSro,6616
scipy/io/tests/data/array_float32_5d.sav,sha256=VmaBgCD854swYyLouDMHJf4LL6iUNgajEOQf0pUjHjg,7896
scipy/io/tests/data/array_float32_6d.sav,sha256=lb7modI0OQDweJWbDxEV2OddffKgMgq1tvCy5EK6sOU,19416
scipy/io/tests/data/array_float32_7d.sav,sha256=pqLWIoxev9sLCs9LLwxFlM4RCFwxHC4Q0dEEz578mpI,3288
scipy/io/tests/data/array_float32_8d.sav,sha256=R8A004f9XLWvF6eKMNEqIrC6PGP1vLZr9sFqawqM8ZA,13656
scipy/io/tests/data/array_float32_pointer_1d.sav,sha256=sV7qFNwHK-prG5vODa7m5HYK7HlH_lqdfsI5Y1RWDyg,2692
scipy/io/tests/data/array_float32_pointer_2d.sav,sha256=b0brvK6xQeezoRuujmEcJNw2v6bfASLM3FSY9u5dMSg,3256
scipy/io/tests/data/array_float32_pointer_3d.sav,sha256=a_Iyg1YjPBRh6B-N_n_BGIVjFje4K-EPibKV-bPbF7E,13816
scipy/io/tests/data/array_float32_pointer_4d.sav,sha256=cXrkHHlPyoYstDL_OJ15-55sZOOeDNW2OJ3KWhBv-Kk,6680
scipy/io/tests/data/array_float32_pointer_5d.sav,sha256=gRVAZ6jeqFZyIQI9JVBHed9Y0sjS-W4bLseb01rIcGs,7960
scipy/io/tests/data/array_float32_pointer_6d.sav,sha256=9yic-CQiS0YR_ow2yUA2Nix0Nb_YCKMUsIgPhgcJT1c,19480
scipy/io/tests/data/array_float32_pointer_7d.sav,sha256=Rp1s8RbW8eoEIRTqxba4opAyY0uhTuyy3YkwRlNspQU,3352
scipy/io/tests/data/array_float32_pointer_8d.sav,sha256=Wk3Dd2ClAwWprXLKZon3blY7aMvMrJqz_NXzK0J5MFY,13720
scipy/io/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
scipy/io/tests/data/example_2.nc,sha256=wywMDspJ2QT431_sJUr_5DHqG3pt9VTvDJzfR9jeWCk,272
scipy/io/tests/data/example_3_maskedvals.nc,sha256=P9N92jCJgKJo9VmNd7FeeJSvl4yUUFwBy6JpR4MeuME,1424
scipy/io/tests/data/fortran-3x3d-2i.dat,sha256=oYCXgtY6qqIqLAhoh_46ob_RVQRcV4uu333pOiLKgRM,451
scipy/io/tests/data/fortran-mixed.dat,sha256=zTi7RLEnyAat_DdC3iSEcSbyDtAu0aTKwUT-tExjasw,40
scipy/io/tests/data/fortran-sf8-11x1x10.dat,sha256=KwaOrZOAe-wRhuxvmHIK-Wr59us40MmiA9QyWtIAUaA,888
scipy/io/tests/data/fortran-sf8-15x10x22.dat,sha256=5ohvjjOUcIsGimSqDhpUUKwflyhVsfwKL5ElQe_SU0I,26408
scipy/io/tests/data/fortran-sf8-1x1x1.dat,sha256=Djmoip8zn-UcxWGUPKV5wzKOYOf7pbU5L7HaR3BYlec,16
scipy/io/tests/data/fortran-sf8-1x1x5.dat,sha256=Btgavm3w3c9md_5yFfq6Veo_5IK9KtlLF1JEPeHhZoU,48
scipy/io/tests/data/fortran-sf8-1x1x7.dat,sha256=L0r9yAEMbfMwYQytzYsS45COqaVk-o_hi6zRY3yIiO4,64
scipy/io/tests/data/fortran-sf8-1x3x5.dat,sha256=c2LTocHclwTIeaR1Pm3mVMyf5Pl_imfjIFwi4Lpv0Xs,128
scipy/io/tests/data/fortran-si4-11x1x10.dat,sha256=OesvSIGsZjpKZlZsV74PNwy0Co0KH8-3gxL9-DWoa08,448
scipy/io/tests/data/fortran-si4-15x10x22.dat,sha256=OJcKyw-GZmhHb8REXMsHDn7W5VP5bhmxgVPIAYG-Fj4,13208
scipy/io/tests/data/fortran-si4-1x1x1.dat,sha256=1Lbx01wZPCOJHwg99MBDuc6QZKdMnccxNgICt4omfFM,12
scipy/io/tests/data/fortran-si4-1x1x5.dat,sha256=L1St4yiHTA3v91JjnndYfUrdKfT1bWxckwnnrscEZXc,28
scipy/io/tests/data/fortran-si4-1x1x7.dat,sha256=Dmqt-tD1v2DiPZkghGGZ9Ss-nJGfei-3yFXPO5Acpk4,36
scipy/io/tests/data/fortran-si4-1x3x5.dat,sha256=3vl6q93m25jEcZVKD0CuKNHmhZwZKp-rv0tfHoPVP88,68
scipy/io/tests/data/invalid_pointer.sav,sha256=JmgoISXC4r5fSmI5FqyapvmzQ4qpYLf-9N7_Et1p1HQ,1280
scipy/io/tests/data/null_pointer.sav,sha256=P_3a_sU614F3InwM82jSMtWycSZkvqRn1apwd8XxbtE,2180
scipy/io/tests/data/scalar_byte.sav,sha256=dNJbcE5OVDY_wHwN_UBUtfIRd13Oqu-RBEO74g5SsBA,2076
scipy/io/tests/data/scalar_byte_descr.sav,sha256=DNTmDgDWOuzlQnrceER6YJ0NutUUwZ9tozVMBWQmuuY,2124
scipy/io/tests/data/scalar_complex32.sav,sha256=NGd-EvmFZgt8Ko5MP3T_TLwyby6yS0BXM_OW8197hpU,2076
scipy/io/tests/data/scalar_complex64.sav,sha256=gFBWtxuAajazupGFSbvlWUPDYK-JdWgZcEWih2-7IYU,2084
scipy/io/tests/data/scalar_float32.sav,sha256=EwWQw2JTwq99CHVpDAh4R20R0jWaynXABaE2aTRmXrs,2072
scipy/io/tests/data/scalar_float64.sav,sha256=iPcDlgF1t0HoabvNLWCbSiTPIa9rvVEbOGGmE_3Ilsk,2076
scipy/io/tests/data/scalar_heap_pointer.sav,sha256=JXZbPmntXILsNOuLIKL8qdu8gDJekYrlN9DQxAWve0E,2204
scipy/io/tests/data/scalar_int16.sav,sha256=kDBLbPYGo2pzmZDhyl8rlDv0l6TMEWLIoLtmgJXDMkk,2072
scipy/io/tests/data/scalar_int32.sav,sha256=IzJwLvEoqWLO5JRaHp8qChfptlauU-ll3rb0TfDDM8Y,2072
scipy/io/tests/data/scalar_int64.sav,sha256=-aSHQRiaE3wjAxINwuLX33_8qmWl4GUkTH45elTkA-8,2076
scipy/io/tests/data/scalar_string.sav,sha256=AQ7iZ8dKk9QfnLdP9idKv1ojz0M_SwpL7XAUmbHodDQ,2124
scipy/io/tests/data/scalar_uint16.sav,sha256=928fmxLsQM83ue4eUS3IEnsLSEzmHBklDA59JAUvGK8,2072
scipy/io/tests/data/scalar_uint32.sav,sha256=X3RbPhS6_e-u-1S1gMyF7s9ys7oV6ZNwPrJqJ6zIJsk,2072
scipy/io/tests/data/scalar_uint64.sav,sha256=ffVyS2oKn9PDtWjJdOjSRT2KZzy6Mscgd4u540MPHC4,2076
scipy/io/tests/data/struct_arrays.sav,sha256=TzH-Gf0JgbP_OgeKYbV8ZbJXvWt1VetdUr6C_ziUlzg,2580
scipy/io/tests/data/struct_arrays_byte_idl80.sav,sha256=oOmhTnmKlE60-JMJRRMv_zfFs4zqioMN8QA0ldlgQZo,1388
scipy/io/tests/data/struct_arrays_replicated.sav,sha256=kXU8j9QI2Q8D22DVboH9fwwDQSLVvuWMJl3iIOhUAH8,2936
scipy/io/tests/data/struct_arrays_replicated_3d.sav,sha256=s3ZUwhT6TfiVfk4AGBSyxYR4FRzo4sZQkTxFCJbIQMI,4608
scipy/io/tests/data/struct_inherit.sav,sha256=4YajBZcIjqMQ4CI0lRUjXpYDY3rI5vzJJzOYpjWqOJk,2404
scipy/io/tests/data/struct_pointer_arrays.sav,sha256=fkldO6-RO2uAN_AI9hM6SEaBPrBf8TfiodFGJpViaqg,2408
scipy/io/tests/data/struct_pointer_arrays_replicated.sav,sha256=eKVerR0LoD9CuNlpwoBcn7BIdj3-8x56VNg--Qn7Hgc,2492
scipy/io/tests/data/struct_pointer_arrays_replicated_3d.sav,sha256=vsqhGpn3YkZEYjQuI-GoX8Jg5Dv8A2uRtP0kzQkq4lg,2872
scipy/io/tests/data/struct_pointers.sav,sha256=Zq6d5V9ZijpocxJpimrdFTQG827GADBkMB_-6AweDYI,2268
scipy/io/tests/data/struct_pointers_replicated.sav,sha256=aIXPBIXTfPmd4IaLpYD5W_HUoIOdL5Y3Hj7WOeRM2sA,2304
scipy/io/tests/data/struct_pointers_replicated_3d.sav,sha256=t1jhVXmhW6VotQMNZ0fv0sDO2pkN4EutGsx5No4VJQs,2456
scipy/io/tests/data/struct_scalars.sav,sha256=LYICjERzGJ_VvYgtwJ_Up2svQTv8wBzNcVD3nsd_OPg,2316
scipy/io/tests/data/struct_scalars_replicated.sav,sha256=lw3fC4kppi6BUWAd4n81h8_KgoUdiJl5UIt3CvJIuBs,2480
scipy/io/tests/data/struct_scalars_replicated_3d.sav,sha256=xVAup6f1dSV_IsSwBQC3KVs0eLEZ6-o5EaZT9yUoDZI,3240
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav,sha256=gjv__ng9xH_sm34hyxCbCgO4AP--PZAfDOArH5omkjM,3586
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav,sha256=H0LLyv2lc2guzYGnx4DWXU6vB57JrRX-G9Dd4qGh0hM,3586
scipy/io/tests/data/test-44100Hz-be-1ch-4bytes.wav,sha256=KKz9SXv_R3gX_AVeED2vyhYnj4BvD1uyDiKpCT3ulZ0,17720
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav,sha256=YX1g8qdCOAG16vX9G6q4SsfCj2ZVk199jzDQ8S0zWYI,72
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav,sha256=bFrsRqw0QXmsaDtjD6TFP8hZ5jEYMyaCmt-ka_C6GNk,1024
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav,sha256=zMnhvZvrP4kyOWKVKfbBneyv03xvzgqXYhHNxsAxDJ4,13
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-rf64.wav,sha256=GSJpCuezlvHbhP3Cr4jNWmz4zG46XZ6jci2fWtiMN0k,17756
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav,sha256=9qTCvpgdz3raecVN1ViggHPnQjBf47xmXod9iCDsEik,17720
scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav,sha256=EqYBnEgTxTKvaTAtdA5HIl47CCFIje93y4hawR6Pyu0,7792
scipy/io/tests/data/test-8000Hz-be-3ch-5S-24bit.wav,sha256=hGYchxQFjrtvZCBo0ULi-xdZ8krqXcKdTl3NSUfqe8k,90
scipy/io/tests/data/test-8000Hz-le-1ch-10S-20bit-extra.wav,sha256=h8CXsW5_ShKR197t_d-TUTlgDqOZ-7wK_EcVGucR-aY,74
scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav,sha256=BoUCDct3GiY_JJV_HoghF3mzAebT18j02c-MOn19KxU,70
scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav,sha256=R6EJshvQp5YVR4GB9u4Khn5HM1VMfJUj082i8tkBIJ8,1644
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-inconsistent.wav,sha256=t2Mgri3h6JLQDekrwIhDBOaG46OUzHynUz0pKbvOpNU,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-rf64.wav,sha256=iSGyqouX53NaEB33tzKXa11NRIY97GG40_pqWF_k5LQ,126
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav,sha256=yCv0uh-ux_skJsxeOjzog0YBk3ZQO_kw5HJHMqtVyI0,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav,sha256=oiMVsQV9-qGBz_ZwsfAkgA9BZXNjXbH4zxCGvvdT0RY,120
scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav,sha256=e97XoPrPGJDIh8nO6mii__ViY5yVlmt4OnPQoDN1djs,134
scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav,sha256=wbonKlzvzQ_bQYyBsj-GwnihZOhn0uxfKhL_nENCGNc,150
scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav,sha256=Uu5QPQcbtnFlnxOd4zFGxpiTC4wgdp6JOoYJ2VMZIU0,164
scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav,sha256=1F67h8tr2xz0C5K21T9y9gspcGA0qnSOzsl2vjArAMs,116
scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav,sha256=TJvGU7GpgXdCrdrjzMlDtpieDMnDK-lWMMqlWjT23BY,89
scipy/io/tests/data/various_compressed.sav,sha256=H-7pc-RCQx5y6_IbHk1hB6OfnhvuPyW6EJq4EwI9iMc,1015
scipy/io/tests/test_fortran.py,sha256=Oc1omSrxrhyELm5hHA_m3TL-hXcivkBw3EMYLx7KCAY,7767
scipy/io/tests/test_idl.py,sha256=hy3ER4-qVpni6T9rvhkra80FqoGRUaQGDLgU_2US9TQ,20992
scipy/io/tests/test_mmio.py,sha256=bYigf3e8Sn6OQUAv3GOX4YI8sAp3KIGZrVqD72jGrJk,28707
scipy/io/tests/test_netcdf.py,sha256=izzpjJbXiIWPAy_xL5Mo2x5-b65LJtjxJR6P8cgnGiQ,19950
scipy/io/tests/test_paths.py,sha256=yfqQtS6YQkg2SFa4_YbGBqZlIhd-brGut6jSRDtv4yc,3271
scipy/io/tests/test_wavfile.py,sha256=mUsRyrb5-FkDo_GeyjV3_xD_w9lj4-Gdgcn5sdXzk6c,17172
scipy/io/wavfile.py,sha256=SgCDDgxKz7KelbTzMdMbo5-tK9KeV7SVS7tUUtZiQYM,29540
scipy/linalg.pxd,sha256=SUm9fRHyF3s5-nhG0PWlenOlEEXtMRBEW_5X2q0ufGs,54
scipy/linalg/__init__.py,sha256=byYkkwVKNJuoJzNiksMlhQvd3RxaZU35EnJImC3bhlw,7753
scipy/linalg/__pycache__/__init__.cpython-311.pyc,,
scipy/linalg/__pycache__/_basic.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_cholesky.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_cossin.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_ldl.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_lu.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_polar.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_qr.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_qz.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_schur.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_svd.cpython-311.pyc,,
scipy/linalg/__pycache__/_expm_frechet.cpython-311.pyc,,
scipy/linalg/__pycache__/_interpolative_backend.cpython-311.pyc,,
scipy/linalg/__pycache__/_matfuncs.cpython-311.pyc,,
scipy/linalg/__pycache__/_matfuncs_inv_ssq.cpython-311.pyc,,
scipy/linalg/__pycache__/_matfuncs_sqrtm.cpython-311.pyc,,
scipy/linalg/__pycache__/_misc.cpython-311.pyc,,
scipy/linalg/__pycache__/_procrustes.cpython-311.pyc,,
scipy/linalg/__pycache__/_sketches.cpython-311.pyc,,
scipy/linalg/__pycache__/_solvers.cpython-311.pyc,,
scipy/linalg/__pycache__/_special_matrices.cpython-311.pyc,,
scipy/linalg/__pycache__/_testutils.cpython-311.pyc,,
scipy/linalg/__pycache__/basic.cpython-311.pyc,,
scipy/linalg/__pycache__/blas.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_cholesky.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_lu.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_qr.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_schur.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_svd.cpython-311.pyc,,
scipy/linalg/__pycache__/interpolative.cpython-311.pyc,,
scipy/linalg/__pycache__/lapack.cpython-311.pyc,,
scipy/linalg/__pycache__/matfuncs.cpython-311.pyc,,
scipy/linalg/__pycache__/misc.cpython-311.pyc,,
scipy/linalg/__pycache__/special_matrices.cpython-311.pyc,,
scipy/linalg/_basic.py,sha256=_sO9zuYe-QjFr5QOU2Shj42iemSglpBdup_b9JTKGXo,71114
scipy/linalg/_blas_subroutines.h,sha256=YvD5DtaiV0EWMiIiSk6Qq8y1wPYe-F_az_AC7HdgOZc,18354
scipy/linalg/_cythonized_array_utils.cp311-win_amd64.dll.a,sha256=r5DMQ333grllbmkSByx-McIFRXsEzZ2fD5goE2pevQ4,1736
scipy/linalg/_cythonized_array_utils.cp311-win_amd64.pyd,sha256=GgJheUbIF9oZdnqed2bdHMYJEy9kt-v9fLpQSOCF0yU,579584
scipy/linalg/_cythonized_array_utils.pxd,sha256=fyp2eQgNSXX88dHrIV5qUuZ-fTEU-8JdXKOTLagAeK8,930
scipy/linalg/_cythonized_array_utils.pyi,sha256=scRj6ew53DtnyIry4gVV3IsZlbwiNZJeptv_0icKE2g,356
scipy/linalg/_decomp.py,sha256=7gjiZD0UpcUINQIrPbQpALS4HwCbVHbBz08jjFTeD-g,63513
scipy/linalg/_decomp_cholesky.py,sha256=kNBV-ZMgHgtLFf0P4p7Q0r517PVQPTDpVroTc-3DqlU,12919
scipy/linalg/_decomp_cossin.py,sha256=mwXU7s2k92ser1wQBZUTWJL6vbyFyKO2KOuwKwRK7sA,9194
scipy/linalg/_decomp_ldl.py,sha256=fx4qmHmF_JuJ3R_r9aqcn7UZG4jTui6Vy5SqmIiD2Ns,12888
scipy/linalg/_decomp_lu.py,sha256=hJtvRhKQZAheLFrxncEprb79wi-bKxY-J6pQXWnSho4,13317
scipy/linalg/_decomp_lu_cython.cp311-win_amd64.dll.a,sha256=n1FobdmYK7YHCku6B0tjYo12NvElXwVioussrrNLVSM,1664
scipy/linalg/_decomp_lu_cython.cp311-win_amd64.pyd,sha256=8cM5nspjmjNgJxl99mv5xzjS-zg5ua_bqB2LRBSKe34,244224
scipy/linalg/_decomp_lu_cython.pyi,sha256=bwyBF7nl-jGGrmj_D16QSXx7MBXAVtU3k4eswqKohCA,297
scipy/linalg/_decomp_polar.py,sha256=4KAJQAgKoN6W_onEM-m3oGqfwCK3__ASC7UFMFdKcac,3689
scipy/linalg/_decomp_qr.py,sha256=a1PbgvHHzMebBjt5Zvq9yIKuJcj8-OvAkZ4hmkNeLCw,15404
scipy/linalg/_decomp_qz.py,sha256=RMxX7pLyNuFhvTKwRnuaR0NraFwuZhvC8DBlIPc8I4Q,16779
scipy/linalg/_decomp_schur.py,sha256=gHghiJfxYUl_hvVGVaqhZ8vJO0VVwTFTbd5pxFLzJ8w,11041
scipy/linalg/_decomp_svd.py,sha256=YuZidLHymDOkJhCTvSSwEM2kyzNHieXPKMtE84E3RJQ,16238
scipy/linalg/_decomp_update.cp311-win_amd64.dll.a,sha256=qeSYqk6o0sLqbCLeOfcTpS0QXJszGs_GUujyT502JHE,1628
scipy/linalg/_decomp_update.cp311-win_amd64.pyd,sha256=4fx-bNjIyumcfUh86Zbk2Hj10ypnGHanjcL7iQEZPls,336384
scipy/linalg/_expm_frechet.py,sha256=m4h3Ibq3Jlh2LMuHNoRsB_LsGHDmTo5XyCdPcudx3Zs,12741
scipy/linalg/_fblas.cp311-win_amd64.dll.a,sha256=c69xaXq03ud57a8MMzZao8wzds_ET9Z0yRMmYqWzgoI,1532
scipy/linalg/_fblas.cp311-win_amd64.pyd,sha256=_ksoQwFXa8zQdqYQ2QZv0fGf7rumuILGBW2TT9mcohI,711168
scipy/linalg/_flapack.cp311-win_amd64.dll.a,sha256=eW7zRbk73vCEpphQUVyySMoLTaVXmH8u1sfYhwl9sxc,1560
scipy/linalg/_flapack.cp311-win_amd64.pyd,sha256=wWwDi0U8r-T6zZRvPNFg9dnKumFSpU4UgHZFN1Tu_xE,1997312
scipy/linalg/_interpolative.cp311-win_amd64.dll.a,sha256=MrJ-167nen6qZCbKgi9CZsDCBEFBKySmu-TN4S0ZUtQ,1628
scipy/linalg/_interpolative.cp311-win_amd64.pyd,sha256=oj7J4S-dmVdjPZahIHK49OHzoTn_xNQBY2ll-1IiU4c,882176
scipy/linalg/_interpolative_backend.py,sha256=mwdx_kKIlNkWDBeLqx7WnD2ce4UEo2nXqCSyKHny-9s,46873
scipy/linalg/_lapack_subroutines.h,sha256=m28-LKqeKyorgxl96RszhKAKBqbDSbvySmNMV_jnjfI,240854
scipy/linalg/_matfuncs.py,sha256=NH6vAafd0BKH7-YtTf8WSNXd965M2TrLQMWYR3JtIBQ,25276
scipy/linalg/_matfuncs_expm.cp311-win_amd64.dll.a,sha256=kjQnQtuWPAMz4T2auvYN-3aICFgFHE58xeDAYil2Ig4,1628
scipy/linalg/_matfuncs_expm.cp311-win_amd64.pyd,sha256=ME76Jzon2pUO2Rlpz1t8tBN13544Yp0PjurKQw7JHLo,481792
scipy/linalg/_matfuncs_expm.pyi,sha256=J4OGtavgQPeZXtpBbWDQMH08jKqrdM028ZpWzyEoDfI,193
scipy/linalg/_matfuncs_inv_ssq.py,sha256=r9jQZ3UTugs8quso43y-elJ3jScg60XZu2oJuJ1CDsk,28981
scipy/linalg/_matfuncs_sqrtm.py,sha256=moDtJg749KvY89uDbewrqANqNdetlXBLunEe8TIgUHo,7046
scipy/linalg/_matfuncs_sqrtm_triu.cp311-win_amd64.dll.a,sha256=2rTp4InkKbDpZKZ9p_Un2PwnO6d0MvO8lEqmGfLJyJw,1704
scipy/linalg/_matfuncs_sqrtm_triu.cp311-win_amd64.pyd,sha256=tZavDWpClKAXRajQaCXOriRSds8WiaQUV96QQnYxIZM,253440
scipy/linalg/_misc.py,sha256=GuAl0DgvKf35a2oyvYgcud84O0e3DCQLSUdR2AzXE3k,6474
scipy/linalg/_procrustes.py,sha256=5s-CF7-cmMR7NRJTqHn1FgozjMKLpmEOMImID7wc7o4,2853
scipy/linalg/_sketches.py,sha256=nVwWE2o7wZw69rFbEDoosoZoNm31jW1iM9fxhdz5BnU,6324
scipy/linalg/_solve_toeplitz.cp311-win_amd64.dll.a,sha256=KyjVnD8fld4JMvwpt90p5Ts-U6BO1IC8USs4TaahpvM,1640
scipy/linalg/_solve_toeplitz.cp311-win_amd64.pyd,sha256=BFpeRyCXjqJDLsdC0Xy6XInGeSrL9X21xM1Tv6M4a4I,273920
scipy/linalg/_solvers.py,sha256=iy2zCyQPTkCEG2fhLZjF0L1t_xd7Ef-hPYQFgJTVBmI,29135
scipy/linalg/_special_matrices.py,sha256=0MP9SeufC5OMsef-dRH42mSYTQGMJs4sLe_tpxS1c2c,38313
scipy/linalg/_testutils.py,sha256=s8zF_A9X4ToKUuwd5QUToC1YoQg7QcFr5UgruaCat2U,1872
scipy/linalg/basic.py,sha256=cY1Q7GWjObxvdOHgB8EQWT3TiQbmXzWfyLOAOpvAGN4,776
scipy/linalg/blas.py,sha256=nX5TZ8folh34phVNhfQWi9W01u8bCZQIde6UinRDHVk,12169
scipy/linalg/cython_blas.cp311-win_amd64.dll.a,sha256=3-3cd6_74CSOad84jqzOgFHtw6VaW_UxHblvxK7EQt8,1592
scipy/linalg/cython_blas.cp311-win_amd64.pyd,sha256=-4ESchQIvmIPSkG7Hy2QhvOkqpcv5p4OmC0FHJg1BDc,279552
scipy/linalg/cython_blas.pxd,sha256=voPCvko12ZS_Bve1IvptSdsq6fu5KXa8ztkLU-zmVF4,15761
scipy/linalg/cython_blas.pyx,sha256=LTkxebc7kG4inZPJdVIr-Y5K5xadbi1O3eHje-BQXXY,66734
scipy/linalg/cython_lapack.cp311-win_amd64.dll.a,sha256=2I9q4f0ajVM5vO2bilvMFMOANlPrTXCmkEM4HNfY4tE,1616
scipy/linalg/cython_lapack.cp311-win_amd64.pyd,sha256=xJAK8Hy_lbnGtmpXXxWbvUPCLYjtSH1guEE6EDgwkIw,526848
scipy/linalg/cython_lapack.pxd,sha256=Up9I8aC8Q6olO0WmMfipi7g6micBTiKAztfpZ0zXOeE,206084
scipy/linalg/cython_lapack.pyx,sha256=XfsRB1Aw-8zVeZ_1m20rQNs8msDiHCtki2iurpKoZao,719057
scipy/linalg/decomp.py,sha256=lHjiR7giOLALFVRUnaLgUZtzkJlsOvdbgdxJSezWzJI,731
scipy/linalg/decomp_cholesky.py,sha256=aWQWVpSmXuxOlmibf4weokAbcumzt3B4bWHNcaSeKTU,670
scipy/linalg/decomp_lu.py,sha256=OpAhkh9LuGzeF3xsNrV7LnLNH1r9BKIRjEykPq6J7Mw,614
scipy/linalg/decomp_qr.py,sha256=dj9Ip_NL9_DhbTMvOu3Uf73KFxzAWw8Zk5AkW_x8Ig4,587
scipy/linalg/decomp_schur.py,sha256=BTSI6wpUMZewPGsaY_t_iJVWH00YO1unn3J8M5cEA0E,623
scipy/linalg/decomp_svd.py,sha256=6vTAujJiNDCu1-UTkPcPmWkT9s2BXSi9H9d_o-RZCow,652
scipy/linalg/interpolative.py,sha256=a33573E5xJCAsYQltbzEC2WJi2QyF3Lowk0khyp3wRk,33266
scipy/linalg/lapack.py,sha256=0LOom2b0ImoQDS8iA9GJ0U_csRsIswwjfZZp0MZ2xc0,16696
scipy/linalg/matfuncs.py,sha256=vS2L0NIDTE4Q8FSeE3uljLVUqmhsTXY_JUbTLvbYsuA,767
scipy/linalg/misc.py,sha256=SY3ronlv05KREUi0vhxT3ruSPUg6MZmRoZWgXJh-7ZQ,613
scipy/linalg/special_matrices.py,sha256=0gdutxoyqAuYHgKppzgGBgDS2pB1e7w5DwvkO7TDWD4,779
scipy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/linalg/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_blas.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_cython_blas.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_cython_lapack.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_cythonized_array_utils.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cholesky.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cossin.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_ldl.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_lu.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_polar.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_update.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_extending.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_fblas.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_interpolative.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_lapack.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_matfuncs.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_matmul_toeplitz.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_procrustes.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_sketches.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_solve_toeplitz.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_solvers.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_special_matrices.cpython-311.pyc,,
scipy/linalg/tests/_cython_examples/extending.pyx,sha256=IX6oqTyEBESOfmGNK2LZeNY9cH3OPfZB6iC_wTecHzM,888
scipy/linalg/tests/_cython_examples/meson.build,sha256=4qNEQVI1Ss_uIAscdgPlmr_uRpTmcgiH0s6XwWqp8dQ,697
scipy/linalg/tests/data/carex_15_data.npz,sha256=E_PhSRqHa79Z1-oQrSnB-bWZaiq5khbzHVv81lkBLB4,34462
scipy/linalg/tests/data/carex_18_data.npz,sha256=Wfg5Rn8nUrffb7bUCUOW7dMqWSm3ZPf_oeZmZDHmysY,161487
scipy/linalg/tests/data/carex_19_data.npz,sha256=OOj8ewQd8LI9flyhXq0aBl5kZ2Ee-ahIzH25P4Ct_Yc,34050
scipy/linalg/tests/data/carex_20_data.npz,sha256=FOIi00pxGMcoShZ1xv7O7ne4TflRpca6Kl7p_zBU-h0,31231
scipy/linalg/tests/data/carex_6_data.npz,sha256=GyoHNrVB6_XEubTADW2rKB5zyfuZE8biWBp4Gze2Avk,15878
scipy/linalg/tests/data/gendare_20170120_data.npz,sha256=o9-rRR2dXCAkPg7YXNi2yWV2afuaD4O1vhZVhXg9VbU,2164
scipy/linalg/tests/test_basic.py,sha256=riYbUvUoaPnQnvm2NBWidCi4BknaP4jKaTPBo85k7fo,76827
scipy/linalg/tests/test_blas.py,sha256=_4-mo4JV8tz4XrRdyI3QJX4-1wgK3Fhfav09axkNcDc,41956
scipy/linalg/tests/test_cython_blas.py,sha256=w-Xg6YtJKzlhfD5EKninnVnvmwJuDYnffM-kzotXoCE,4205
scipy/linalg/tests/test_cython_lapack.py,sha256=FuwDgBZSL9JtQSLjW5-0Bueom1sBYLd4iCwZT2EGX3k,818
scipy/linalg/tests/test_cythonized_array_utils.py,sha256=DhXfE4AUbdX9X5bDhcTvf43-haLv4ZrJ6sqBMshkOvc,4304
scipy/linalg/tests/test_decomp.py,sha256=DiapmXOKYhKUWpnig1r6_TuTxti3IP2K6tuR0WaqGVo,118032
scipy/linalg/tests/test_decomp_cholesky.py,sha256=mqJiEJSLV72YnoI9Ht0pT6ZtJDgaRMW1VPlcTHUPDt0,10011
scipy/linalg/tests/test_decomp_cossin.py,sha256=YnnP5FMr9D7S3wB0tEf3dJN733AlPPQoBlYmu9bx0fE,6112
scipy/linalg/tests/test_decomp_ldl.py,sha256=Qt5tWnNSs6k3dCTWAKRuWDt5U2A8ibo1J-_BF_nJISI,5080
scipy/linalg/tests/test_decomp_lu.py,sha256=k-ToCI62Y7a2NUyzv83EbCtHWHfC5MLSje9r6_8GrYE,12938
scipy/linalg/tests/test_decomp_polar.py,sha256=IQBL6pZnIVLC_-5soVUdHLYxgcQCDl-K8ZEqdXtMYds,2805
scipy/linalg/tests/test_decomp_update.py,sha256=xIhcW4BRZWWRX4hPZsGiEaISxpU7MGMjTKdbiltgabs,70190
scipy/linalg/tests/test_extending.py,sha256=C51MjNAx0te6n8_DSkvKzSn1cijW_ajAdgKjQMXwNXA,1613
scipy/linalg/tests/test_fblas.py,sha256=FoEZpLFpdle1scpFXl5CeVTEH31c5GBxxW5FNPx_Usg,19294
scipy/linalg/tests/test_interpolative.py,sha256=feCEd-BNhbvJV0HJpCmfDQt0uaQhk1LK6t3waSpbyYI,9210
scipy/linalg/tests/test_lapack.py,sha256=dFHfBi9PL_KOPrqhFgti-ErxpFVTuZCISUaggs9ACI4,132383
scipy/linalg/tests/test_matfuncs.py,sha256=b_Oy5O-pqhKvUZugiLOHnQw2UIdrKUbQx4pQ77kDHm0,42148
scipy/linalg/tests/test_matmul_toeplitz.py,sha256=lQ3KFHSAJLGP9bqyvHfnC2CqXujmXBz_dQ2ScEmGh1M,4170
scipy/linalg/tests/test_procrustes.py,sha256=I85qCqgsofvNDfpuD0pcACbu2eIhvW6EaQ3aiUS35hw,7188
scipy/linalg/tests/test_sketches.py,sha256=RMUKNRQTj1o08CGHGf68bsES-jzLuN0AswGG5aNXsk0,4078
scipy/linalg/tests/test_solve_toeplitz.py,sha256=NMAMm-jzyjh5JG68gCjS9F5oErKU-2a2SG90tJ05fig,4725
scipy/linalg/tests/test_solvers.py,sha256=fpyhv8yNbyHxWhZvcueIM7kK_wRyWLmzC9BkQawHCuI,32584
scipy/linalg/tests/test_special_matrices.py,sha256=efzkczz2R2SWmRLuLZV2LsOpYNudHYVy-AB4pLZ0Egw,24356
scipy/misc/__init__.py,sha256=yGB5XvVr2KLtK1e40nBG1TTne-7oxFrwSYufByVeUDE,1793
scipy/misc/__pycache__/__init__.cpython-311.pyc,,
scipy/misc/__pycache__/_common.cpython-311.pyc,,
scipy/misc/__pycache__/common.cpython-311.pyc,,
scipy/misc/__pycache__/doccer.cpython-311.pyc,,
scipy/misc/_common.py,sha256=6UkM80cLptkn_NdOuhU09nGYQB9Ns7wYRPSWWb3_GPY,11497
scipy/misc/ascent.dat,sha256=6KhJOUhEY6uAUa7cW0CqJiqzOpHWRYps0TxqHK1aAj0,527630
scipy/misc/common.py,sha256=HsTgk6Nc9kIGh5QvQ01xsoS8OcyyVlvHojQOgpFWj-g,622
scipy/misc/doccer.py,sha256=NQvWWhsiS5fZXEUjbdWlj1OMjqfJyhZdzUe4DhJJNe8,1503
scipy/misc/ecg.dat,sha256=8grTNl-5t_hF0OXEi2_mcIE3fuRmw6Igt_afNciVi68,119035
scipy/misc/face.dat,sha256=nYsLTQgTE-K0hXSMdwRy5ale0XOBRog9hMcDBJPoKIY,1581821
scipy/misc/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/misc/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/misc/tests/__pycache__/test_common.cpython-311.pyc,,
scipy/misc/tests/__pycache__/test_config.cpython-311.pyc,,
scipy/misc/tests/__pycache__/test_doccer.cpython-311.pyc,,
scipy/misc/tests/test_common.py,sha256=rfcxQnBUc22G95MDYoYRmIciDjL8Pgmrf0fbMp-pqGs,859
scipy/misc/tests/test_config.py,sha256=Ma6jMr-KuIuDdFFz88ZHmFcawmOugzfJnspZv9Aqda4,1288
scipy/misc/tests/test_doccer.py,sha256=wmoKV2T7y_6K0P2RtN8d6IvxXCvuctzQkjgmVgRMSnc,4196
scipy/ndimage/__init__.py,sha256=Oo-CeM7DPYAGMsDW39xOMPXINXdq0tSRaASScCIJj1g,5158
scipy/ndimage/__pycache__/__init__.cpython-311.pyc,,
scipy/ndimage/__pycache__/_filters.cpython-311.pyc,,
scipy/ndimage/__pycache__/_fourier.cpython-311.pyc,,
scipy/ndimage/__pycache__/_interpolation.cpython-311.pyc,,
scipy/ndimage/__pycache__/_measurements.cpython-311.pyc,,
scipy/ndimage/__pycache__/_morphology.cpython-311.pyc,,
scipy/ndimage/__pycache__/_ni_docstrings.cpython-311.pyc,,
scipy/ndimage/__pycache__/_ni_support.cpython-311.pyc,,
scipy/ndimage/__pycache__/filters.cpython-311.pyc,,
scipy/ndimage/__pycache__/fourier.cpython-311.pyc,,
scipy/ndimage/__pycache__/interpolation.cpython-311.pyc,,
scipy/ndimage/__pycache__/measurements.cpython-311.pyc,,
scipy/ndimage/__pycache__/morphology.cpython-311.pyc,,
scipy/ndimage/_ctest.cp311-win_amd64.dll.a,sha256=QkaHODsgkeHlXadH72C1PL_DChDY0ES_yzeaQBIZRvM,1532
scipy/ndimage/_ctest.cp311-win_amd64.pyd,sha256=a85jqQXrT1DVgAS_6hdH5gLgULMxWYBfbQSpAI_OStk,16896
scipy/ndimage/_cytest.cp311-win_amd64.dll.a,sha256=v0kivd2R5zALMfy9TjWHIV6DDdBuU095UhzkDHkB_50,1544
scipy/ndimage/_cytest.cp311-win_amd64.pyd,sha256=y7dGIEs4sfTMumS8L5bz5wZl6GyJqolAX6Yq6iRE3Xw,75264
scipy/ndimage/_filters.py,sha256=gRyVINdXhG4BbOIBpBqZugyamigW7IxlfYSYn_Xj-Iw,67591
scipy/ndimage/_fourier.py,sha256=PTcoyytgZc_dFBsPuYriCy6oJv5HVz85zdSppt4GogM,11572
scipy/ndimage/_interpolation.py,sha256=ONfN8ya9PGNPOSlxW_mptIfJd0TLjoTba8XNNjteGGA,37610
scipy/ndimage/_measurements.py,sha256=XJSZI4ASEzhsR85Fri9BwTOI1JXBFQKmVcR97HKk174,57588
scipy/ndimage/_morphology.py,sha256=6X7YRYfSLWnfBcDL6l2tpvSB8Qlzpk1jn6DPT1S7gf0,98476
scipy/ndimage/_nd_image.cp311-win_amd64.dll.a,sha256=-xTdeTbIZhKgtJG-OAXAUyy_mnwEw6BzasTFBF9w1HE,1568
scipy/ndimage/_nd_image.cp311-win_amd64.pyd,sha256=huRduz3NrwPfrYtAmRTyOlJ2EE7u5IoUoZq93Vpo3io,176128
scipy/ndimage/_ni_docstrings.py,sha256=gUp13eFtd2wqZwlpI7O6SqDIlY7bGikdujvOAntqwf8,8713
scipy/ndimage/_ni_label.cp311-win_amd64.dll.a,sha256=Ob0yYUkIULyir5QP--nP0DgfzoDl_z_zXFL6ucY1aWo,1568
scipy/ndimage/_ni_label.cp311-win_amd64.pyd,sha256=GVodkwdPUgqYSJJWhNfaZE5ftfcVvn__xfYf45xYrCA,411136
scipy/ndimage/_ni_support.py,sha256=G75Uvswo_Bhymzov2qCa1CnSV589IA5I-DBOIRbE9Zk,4732
scipy/ndimage/filters.py,sha256=pBSTBZeUY3XAJCIFdL1UZmB3OM6KJld2jhzPVWHpOYs,1003
scipy/ndimage/fourier.py,sha256=Mg8ym6fd2BXBFSrUiwrW3GUoeDTYXbdOqQe75EJiKYw,620
scipy/ndimage/interpolation.py,sha256=t5_hbCDE3G06Fd-A7wozt_Tndjlbj3z6jRQPtjiBReo,686
scipy/ndimage/measurements.py,sha256=NNTrZLZSbU6hO42Tj49me-S1A-V-pnODbpxkm4ehLOI,812
scipy/ndimage/morphology.py,sha256=6axnW-p_Fp3Bm-21zYoaaz-1SdNxEPYCl8SoeRXwDQQ,992
scipy/ndimage/tests/__init__.py,sha256=g9cOtAMtC1wnBpDWjSfseBbjiZgXqY6pFarjyxqytNw,378
scipy/ndimage/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_c_api.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_datatypes.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_filters.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_fourier.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_interpolation.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_measurements.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_morphology.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_ni_support.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_splines.cpython-311.pyc,,
scipy/ndimage/tests/data/label_inputs.txt,sha256=oBKOjlyOqkDERFefuyjlsPeVfGRyfmDc-uiawuMey4A,315
scipy/ndimage/tests/data/label_results.txt,sha256=hNPE8YOPm5MZ169I4c5JnG2cWxleOlKddG1VdA-Fn3Y,4603
scipy/ndimage/tests/data/label_strels.txt,sha256=fLjLCCb72QdX55kKssZdwg262SaV5NgdIG_Bn_gPHSg,294
scipy/ndimage/tests/dots.png,sha256=sgtW-tx0ccBpTT6BSNniioPXlnusFr-IUglK_qOVBBQ,2114
scipy/ndimage/tests/test_c_api.py,sha256=ii8Y7KA9O4TJEosO_bQ8yN3jBS9QvqnmM4r2KlEU0fs,3832
scipy/ndimage/tests/test_datatypes.py,sha256=4qhH8s06BlCG0Jbw5gps_7RDFNuw_D7r4fFtdV8tYqQ,2893
scipy/ndimage/tests/test_filters.py,sha256=qfDulz7QrwH9U0BjLhHsEaA7c1MF1NJ6qL9qgPj6Xjk,95140
scipy/ndimage/tests/test_fourier.py,sha256=ps7RbUW9jpyDlMls4PEUNGUOAVXuD2SreAmzxg3XDi8,6277
scipy/ndimage/tests/test_interpolation.py,sha256=PGjAyhPq1Qe5GFoycMWksl7iduror3u44xeGreA_uZo,55153
scipy/ndimage/tests/test_measurements.py,sha256=Jy7VSAJeByEvbpLX5TQEUj2eWf6ABh5fgfcGxJc8ooU,49907
scipy/ndimage/tests/test_morphology.py,sha256=TQohPYNO5QosENPoHfLprphE7lp62rCDzTU9tPhCOlI,106766
scipy/ndimage/tests/test_ni_support.py,sha256=8DTgqgKOyw6ydsgPfSTeiYyfhcnAS9QjRdMk8slo67A,2549
scipy/ndimage/tests/test_splines.py,sha256=r1rRYS7GD0ZsQWvuobXIBBC1Mh1MdHusr68vZLdhXBU,2264
scipy/odr/__init__.py,sha256=gHT9GMAwFz2ewCC0B1kTenNLG6g8CHNm__5z6vtF_zc,4456
scipy/odr/__odrpack.cp311-win_amd64.dll.a,sha256=t1y1MCorCCkyfBAAfRUcBz-LH11gTfO6YrTMZgJIlH0,1568
scipy/odr/__odrpack.cp311-win_amd64.pyd,sha256=kLjG1YTRoRzCgLYwZgxg0CWsHDutiQNUY5x20BldasE,690688
scipy/odr/__pycache__/__init__.cpython-311.pyc,,
scipy/odr/__pycache__/_add_newdocs.cpython-311.pyc,,
scipy/odr/__pycache__/_models.cpython-311.pyc,,
scipy/odr/__pycache__/_odrpack.cpython-311.pyc,,
scipy/odr/__pycache__/models.cpython-311.pyc,,
scipy/odr/__pycache__/odrpack.cpython-311.pyc,,
scipy/odr/_add_newdocs.py,sha256=nquKKPO9q-4oOImnO766H3wnLIN8dBZJfqPh8BgKJ_8,1162
scipy/odr/_models.py,sha256=Mi8SuWoWXIFqGi3BfgMSc_eu1GQfGRtXSvkeI9fblWQ,8115
scipy/odr/_odrpack.py,sha256=PUA6s-Ev3VyXEJkEkCxjlSXR7GK2m11XwWi-yPGtzlY,43594
scipy/odr/models.py,sha256=_7pQbo0FThkV9yo2NvXC21_SMbNqUiBjeUsxnd9PerM,610
scipy/odr/odrpack.py,sha256=NqRd2QtNcw1-gq4KpkbkddvMF3G78DxKGSFlJ8Day4Q,653
scipy/odr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/odr/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/odr/tests/__pycache__/test_odr.cpython-311.pyc,,
scipy/odr/tests/test_odr.py,sha256=LN0trdBnsLWIBPu00Rv4kc6bJI2ugREtpn_yWPncnhA,22654
scipy/optimize.pxd,sha256=Go47l_Tk8YvXQxnuJNN6UmDEApEKgBUrplitjO4tsUM,40
scipy/optimize/README,sha256=_CUBWHgG5xW7HG5La9gsHQ2Cr7cRidzPOexd4rg3oIA,3297
scipy/optimize/__init__.py,sha256=mSrOMT7Znr0SbsZuX4ODBK7PoIUbGMJ7-U3YXiV8q0w,13588
scipy/optimize/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/__pycache__/_basinhopping.cpython-311.pyc,,
scipy/optimize/__pycache__/_bracket.cpython-311.pyc,,
scipy/optimize/__pycache__/_chandrupatla.cpython-311.pyc,,
scipy/optimize/__pycache__/_cobyla_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_cobyqa_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_constraints.cpython-311.pyc,,
scipy/optimize/__pycache__/_dcsrch.cpython-311.pyc,,
scipy/optimize/__pycache__/_differentiable_functions.cpython-311.pyc,,
scipy/optimize/__pycache__/_differentialevolution.cpython-311.pyc,,
scipy/optimize/__pycache__/_differentiate.cpython-311.pyc,,
scipy/optimize/__pycache__/_direct_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_dual_annealing.cpython-311.pyc,,
scipy/optimize/__pycache__/_hessian_update_strategy.cpython-311.pyc,,
scipy/optimize/__pycache__/_isotonic.cpython-311.pyc,,
scipy/optimize/__pycache__/_lbfgsb_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_linesearch.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_doc.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_highs.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_ip.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_rs.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_simplex.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_util.cpython-311.pyc,,
scipy/optimize/__pycache__/_milp.cpython-311.pyc,,
scipy/optimize/__pycache__/_minimize.cpython-311.pyc,,
scipy/optimize/__pycache__/_minpack_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_nnls.cpython-311.pyc,,
scipy/optimize/__pycache__/_nonlin.cpython-311.pyc,,
scipy/optimize/__pycache__/_numdiff.cpython-311.pyc,,
scipy/optimize/__pycache__/_optimize.cpython-311.pyc,,
scipy/optimize/__pycache__/_qap.cpython-311.pyc,,
scipy/optimize/__pycache__/_remove_redundancy.cpython-311.pyc,,
scipy/optimize/__pycache__/_root.cpython-311.pyc,,
scipy/optimize/__pycache__/_root_scalar.cpython-311.pyc,,
scipy/optimize/__pycache__/_shgo.cpython-311.pyc,,
scipy/optimize/__pycache__/_slsqp_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_spectral.cpython-311.pyc,,
scipy/optimize/__pycache__/_tnc.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion_dogleg.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion_exact.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion_krylov.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion_ncg.cpython-311.pyc,,
scipy/optimize/__pycache__/_tstutils.cpython-311.pyc,,
scipy/optimize/__pycache__/_zeros_py.cpython-311.pyc,,
scipy/optimize/__pycache__/cobyla.cpython-311.pyc,,
scipy/optimize/__pycache__/lbfgsb.cpython-311.pyc,,
scipy/optimize/__pycache__/linesearch.cpython-311.pyc,,
scipy/optimize/__pycache__/minpack.cpython-311.pyc,,
scipy/optimize/__pycache__/minpack2.cpython-311.pyc,,
scipy/optimize/__pycache__/moduleTNC.cpython-311.pyc,,
scipy/optimize/__pycache__/nonlin.cpython-311.pyc,,
scipy/optimize/__pycache__/optimize.cpython-311.pyc,,
scipy/optimize/__pycache__/slsqp.cpython-311.pyc,,
scipy/optimize/__pycache__/tnc.cpython-311.pyc,,
scipy/optimize/__pycache__/zeros.cpython-311.pyc,,
scipy/optimize/_basinhopping.py,sha256=29rYpl9FGaSNGVOrsQWNbOzNMkqlbrBo7dJtIomDX1w,31372
scipy/optimize/_bglu_dense.cp311-win_amd64.dll.a,sha256=CeQfmtmsirqKkmT-MAjYXUGy1VhQ-s3YBqtmZaBxiFM,1592
scipy/optimize/_bglu_dense.cp311-win_amd64.pyd,sha256=p3E_7nxsFWy75Av1tgz1iu9lllwT9NHz-LMc5L-22po,317952
scipy/optimize/_bracket.py,sha256=K0tmSxCLBRauH0EZU7x9Mwk6YAqcXCO3WPUKfdI6YCw,29670
scipy/optimize/_chandrupatla.py,sha256=4d0835GopkftXf5AqmwObM9VUMyEQoRI3MKckjIil9I,24903
scipy/optimize/_cobyla.cp311-win_amd64.dll.a,sha256=CbYTsCapj1NPvz60-G36fowWXMeIC8PJXn3JyMTOazA,1544
scipy/optimize/_cobyla.cp311-win_amd64.pyd,sha256=Iy_0R6WfowUV4w6kAtriyajs4p0Ud_DVLTr5gvpe61c,418816
scipy/optimize/_cobyla_py.py,sha256=Ycqnv7D9YqcDeM1pl-TNS0tMnAAJK9wEsY7KnCbxwUc,11185
scipy/optimize/_cobyqa_py.py,sha256=wgzoK1A3HLBUygBFo0LVT4a6MtW6kyJ-lurbU2KZov8,2655
scipy/optimize/_constraints.py,sha256=ejG1EP0e6MTOR9sPaeXhAvqeGQtGhORT0dbbap3xq_U,23444
scipy/optimize/_dcsrch.py,sha256=HLVdwRymefDXa8QZQyZjca1y-yUWOfYn0Jh8SDs0RM4,25963
scipy/optimize/_differentiable_functions.py,sha256=0DygOEXvn-KzP0tN7NWaDjeml8jGkBejVo_jNVTz0HQ,25428
scipy/optimize/_differentialevolution.py,sha256=KeYt52-vPLXb7akvVcdFgA-u6sdsHjpx15qUqjCBbdQ,87404
scipy/optimize/_differentiate.py,sha256=bcNKL4JtBlFiTTEpoBXCOz8WEZsW0TxTebFMKEZgvDA,39399
scipy/optimize/_direct.cp311-win_amd64.dll.a,sha256=fXc2f6Uce7idn8cl73578BUH4gyAIUMvWuuHMDCMJDs,1544
scipy/optimize/_direct.cp311-win_amd64.pyd,sha256=33PHuRDwrxibxR6X4lq8Tj-AM8Bu0P-GW_t-b72W67E,69632
scipy/optimize/_direct_py.py,sha256=kRtd45Akip2Z-lzRzoLdOkJoxAkr9wTLVJArnpfq8bY,12076
scipy/optimize/_dual_annealing.py,sha256=KSjifOfc1XpKIRXaqcn4w2AIuH8uIWqQgTlupYdjpfI,31975
scipy/optimize/_group_columns.cp311-win_amd64.dll.a,sha256=H91Hn3cI7s1JePWnmr9vGgXXaQr3IDCFQIcC6lmLE_M,1628
scipy/optimize/_group_columns.cp311-win_amd64.pyd,sha256=HMljB8BUwl4Ku3_0NUC8xjwGXU2YLMOClZR9c9Qjdn8,1032192
scipy/optimize/_hessian_update_strategy.py,sha256=1JOMwBUlAg2LWmIwp8OwJEuteU68EtJgsq69LXNrh3w,18761
scipy/optimize/_highs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_highs/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_highs/_highs_constants.cp311-win_amd64.dll.a,sha256=ciI-5DXTmd-I7OvGzPiQxyTKW1htbOpjGPJfsYxS8Nk,1656
scipy/optimize/_highs/_highs_constants.cp311-win_amd64.pyd,sha256=vz7PusBImRYXiIEW26FXuOdllOFfl_RPQ7-WHzcn0gg,948224
scipy/optimize/_highs/_highs_wrapper.cp311-win_amd64.dll.a,sha256=_Q8Te9IH-FxAOd0K1Wtj0gtYbrev9nsaXWvLpNXFfpU,1628
scipy/optimize/_highs/_highs_wrapper.cp311-win_amd64.pyd,sha256=asM3Ry7cu6VRmjKeeZgD6_q8-IGH1ZN1R5Pva1Q8dSg,4544000
scipy/optimize/_highs/src/cython/HConst.pxd,sha256=L15CusfL00lWvNnDBadRg4cfyFQ9OBZjx7DnuWTiQAM,5617
scipy/optimize/_highs/src/cython/Highs.pxd,sha256=DEAT3kbjSV8yG4ODe3pf13WxXXTrtxdXd3iPZB1C-lc,2203
scipy/optimize/_highs/src/cython/HighsIO.pxd,sha256=cyJqwFONXeG4JCUM732mCNtjBqpwlu-NdZTEGNZeWDc,725
scipy/optimize/_highs/src/cython/HighsInfo.pxd,sha256=UVxVCOiJv01N9sdMx7ZJYEWr4Sb3v2JsTyf1oqwli4I,757
scipy/optimize/_highs/src/cython/HighsLp.pxd,sha256=vlYC8YlR4B_nve_g7zc8V7XxDhojrdIl21cAQIwIdfI,1152
scipy/optimize/_highs/src/cython/HighsLpUtils.pxd,sha256=X7RWGM_jqH9iomrdE82vJ3FiQTlwCxOK0dFWwyhBcvk,298
scipy/optimize/_highs/src/cython/HighsModelUtils.pxd,sha256=_wdfARiJz-1uVj4-DCDQFL4qlxtwfSGCEcPENTxMa98,345
scipy/optimize/_highs/src/cython/HighsOptions.pxd,sha256=Q9qcpapCKmpUPQzvnSi8ikl5AwQ1evrw3UilgXO6Xxk,3270
scipy/optimize/_highs/src/cython/HighsRuntimeOptions.pxd,sha256=lGf-04sraouYfLC-nMhelSxckt5KzVBIh6E2fC7IktU,270
scipy/optimize/_highs/src/cython/HighsStatus.pxd,sha256=zbFQHPW_dv9veyurF4ifqzQd4KMvaI-3c812neTMK_Q,351
scipy/optimize/_highs/src/cython/SimplexConst.pxd,sha256=8f0-itFFhq9qWHZCxeGFW1-wu61X6rTt1HhNqcFI_PM,5113
scipy/optimize/_highs/src/cython/highs_c_api.pxd,sha256=Acdk0ptEr5H66e3sFkfcRRRCGp2x-Wl98Z3tp9D7XFw,339
scipy/optimize/_isotonic.py,sha256=O0OAjFFuA00WHNUfFJFVQRK3np7QcRo4TGhosBANsSs,6238
scipy/optimize/_lbfgsb.cp311-win_amd64.dll.a,sha256=2S0ddSRyf4WCqE-G91mujvbP9QGD33MIF7LMnWm6z10,1544
scipy/optimize/_lbfgsb.cp311-win_amd64.pyd,sha256=49FiqFAd2_Hq7ou22q3G0xGhTPxLi6UglMDxxOwbFoY,562176
scipy/optimize/_lbfgsb_py.py,sha256=fVkQ5qw6fdSuc_LxOHHU5Hz_bKZtyeYFkaKHAfKVsiI,21261
scipy/optimize/_linesearch.py,sha256=V_EgqAl5ExJ0eQBz8wAd3nstLGCS2TqbVxeRicF2pnY,28113
scipy/optimize/_linprog.py,sha256=GMivirRzkxk4R3AdqzJYeaolKr3AXAALfu6MPnvKS0Y,30472
scipy/optimize/_linprog_doc.py,sha256=no_OyJgCCqufunfiL-7B1booijS_1OTY8luxvJBQ3b0,63379
scipy/optimize/_linprog_highs.py,sha256=vzNHd5QFirk-fb3j7dBTLF6KC3fHL8fbX9lTcfybCCQ,18013
scipy/optimize/_linprog_ip.py,sha256=LvPFzxHXKOkcTD02x4M4I-eS5i1MVQ6iG3Xjbb5HZOc,46911
scipy/optimize/_linprog_rs.py,sha256=DsKjiN4S4U510ahDcSWyFPIhakRrAXEFBl6i_dl7IiY,23718
scipy/optimize/_linprog_simplex.py,sha256=bWEwU99qgy0fC1G6F6TJHSr765eiKcQXA1_bVgxMJlI,25377
scipy/optimize/_linprog_util.py,sha256=98yfDJILV7iLuBNRTdLmDgbzHBersO54SZfVGJB10Gw,64271
scipy/optimize/_lsap.cp311-win_amd64.dll.a,sha256=tA9D6GRVq43RWLW8mY_NJRYyR7CL1VC3kNLUx-2mKRI,1520
scipy/optimize/_lsap.cp311-win_amd64.pyd,sha256=fn_HTlVTO-CceNSpoCbAMrAH0CXqoW-pEHRlDFBJZqE,177664
scipy/optimize/_lsq/__init__.py,sha256=c4V-tnMyi2dXNNXkjFQ8SdvGANtgbrqXEM-OpdBdZpQ,177
scipy/optimize/_lsq/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/bvls.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/common.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/dogbox.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/least_squares.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/lsq_linear.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/trf.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/trf_linear.cpython-311.pyc,,
scipy/optimize/_lsq/bvls.py,sha256=h9bYh3astIRvMtMGghtEGpG5fDsh74SMRbmgF3HN8YM,5378
scipy/optimize/_lsq/common.py,sha256=zQN7x4qxtX5PCaY2gdYMv4Io0YM4Cit9Eqb_L_k8xrM,21256
scipy/optimize/_lsq/dogbox.py,sha256=KuyTxgJPRumBkrk8KlgZZVhm8tpdmsGNBtPAsj2U1Xs,12013
scipy/optimize/_lsq/givens_elimination.cp311-win_amd64.dll.a,sha256=a9c3MOnweqPzloY8IjnJpUgfk4KuB2nj0nGj4NnOgDY,1676
scipy/optimize/_lsq/givens_elimination.cp311-win_amd64.pyd,sha256=qD3j3s8ceYiJoq1aPyEBP4PL8HVVGyhpAKGf5_LzZyE,210432
scipy/optimize/_lsq/least_squares.py,sha256=nHh4Fzgy28drPE3io6zAe_SfxVj6cOOgSW8RPA-YbU0,40639
scipy/optimize/_lsq/lsq_linear.py,sha256=9Xlb1mb02xqkOja-8Mb6cJDpW4ovNHvyoNRgZSt4QzM,15542
scipy/optimize/_lsq/trf.py,sha256=BBsxUjWZjzdVILU-Zr98t3WiySH_MUIp-unCQlRAo6U,20037
scipy/optimize/_lsq/trf_linear.py,sha256=GEMGyMqWwaVLUa7sCo9iwDmUkuT746oci1iK6KjnJQA,7891
scipy/optimize/_milp.py,sha256=I_UEtdkT2dPPaTmg7PAm4J1TSPzA7LGiFbC12622UxI,15617
scipy/optimize/_minimize.py,sha256=nNXNuKNt_BJW7IkjEHn3l8RkEYew1og6lpHIg67hDao,50563
scipy/optimize/_minpack.cp311-win_amd64.dll.a,sha256=98ppoZmHBbhWQAGaLS4Jp1vaH0VVt2JYp4yBOy4SGbg,1560
scipy/optimize/_minpack.cp311-win_amd64.pyd,sha256=_SpYpnmp-tFbCPrc44gd2iMloGxkQ6CqGAS-6G4bIig,107520
scipy/optimize/_minpack2.cp311-win_amd64.dll.a,sha256=1sUmEikkDn7SHcgrRxzQoUuh7916ZhLUWbaOGyqNbBI,1568
scipy/optimize/_minpack2.cp311-win_amd64.pyd,sha256=RiY6UT_oc1K3opP9Quvzr4R8zOW53UTcpSj8VmTe6I8,75264
scipy/optimize/_minpack_py.py,sha256=0mVjY0Lb3Tx5T8XERroucS8nxY52PJmUXfaZfw-koiM,45951
scipy/optimize/_moduleTNC.cp311-win_amd64.dll.a,sha256=Jrax-jyOFt2uKJA5g9gUUWbkidUZoj_XzdP2U6yu1go,1580
scipy/optimize/_moduleTNC.cp311-win_amd64.pyd,sha256=wzEJDV27SCjaeXozdNqZEB-wOByTTOf30VBW9hWIquw,158720
scipy/optimize/_nnls.py,sha256=uyU0tvulUuhOAGP5JHVbj_fQrZKpypTGmm9OyUEZwcU,5648
scipy/optimize/_nonlin.py,sha256=YCWgcPSX-0XCNiRj_zfTgvZIQbKfqUJGshT9h8kXy3A,51471
scipy/optimize/_numdiff.py,sha256=Rru0OiwWSXkpCGalIswVb-8pBlppK9hPST9AwOfoMNQ,29571
scipy/optimize/_optimize.py,sha256=xcWqAtKKIav4VGznrrEL9OlVCDtrHzcfXCDd_nY-6Fs,150757
scipy/optimize/_pava_pybind.cp311-win_amd64.dll.a,sha256=61RlvCOh9w36LdUL37wjTmURe0A25BmCu4Roy7rhfjE,1608
scipy/optimize/_pava_pybind.cp311-win_amd64.pyd,sha256=_QlG93wvxFPrZpHBINwK-Zl_oqT1Ylx1AJ0E1XSgcGg,288768
scipy/optimize/_qap.py,sha256=WD5UQxYxTcb7Xhatba5J45DcqIVJvVUCtKee0qwol9Y,28562
scipy/optimize/_remove_redundancy.py,sha256=wm3BTNUsnRTd-emKeiFTss68K09fIH4IYyFR7MD3cJg,19291
scipy/optimize/_root.py,sha256=vFiQH7Xt88CZfHp9a7ztUiqLjZ9d5VkPKNjIJp6h8rs,29090
scipy/optimize/_root_scalar.py,sha256=zehSxyAbZI7bZNWLSzeR776P4c85v1La2R2yP7C3a5c,20120
scipy/optimize/_shgo.py,sha256=mqRXc68cAkYHZ9GUfax2J12_Z_sQ65YGsBHOOXUWRRI,64029
scipy/optimize/_shgo_lib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_shgo_lib/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_complex.cpython-311.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_vertex.cpython-311.pyc,,
scipy/optimize/_shgo_lib/_complex.py,sha256=rAXR-2ftZTsPKukYI7Q01gcVe_EimZgHlWZkMebK7s0,51451
scipy/optimize/_shgo_lib/_vertex.py,sha256=z9a-UXpMVD8NXa_y1ePp-Va3b7B1tcAQPmRcky1W2IA,14456
scipy/optimize/_slsqp.cp311-win_amd64.dll.a,sha256=DHQulhp7C-xeNVOW6GwxvJ-aQxVVfXuuX_idYLIm4E4,1532
scipy/optimize/_slsqp.cp311-win_amd64.pyd,sha256=EmWxx_gE37iIuDTo_Rz-JRTYRgF5RkoS8fog98qftCM,108032
scipy/optimize/_slsqp_py.py,sha256=giTnH6yTCD9i4ozSCM-ok6KpHh3kF5Pl6W26pVSgq7g,19505
scipy/optimize/_spectral.py,sha256=j33TVc8_7styCVxKZP76_2C5CsdyFi5lD5TpeBbo54U,8392
scipy/optimize/_tnc.py,sha256=uL9ald4ZzsvDK4W-kPTiIx5Ra8Eqrc21GrvYOaqvGvI,17338
scipy/optimize/_trlib/__init__.py,sha256=5TbV8UJSSEwW_qONgzozoHDgvhhugzqykJzybDCrNj0,536
scipy/optimize/_trlib/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_trlib/_trlib.cp311-win_amd64.dll.a,sha256=OkjqlikNX7LiaDUtwI1jql6bJMe-gOKPear2rDIQ_I4,1532
scipy/optimize/_trlib/_trlib.cp311-win_amd64.pyd,sha256=fEujKVGtpFoinGb-uzvrlEFx-6SBuEp6uYLnKsqubrk,331264
scipy/optimize/_trustregion.py,sha256=1TlWgkWc2EGAkhKfJAwGmwz5GWdxvIHDtVwuvtux6Jk,11105
scipy/optimize/_trustregion_constr/__init__.py,sha256=Y2OLn2HBQ5rxpAfI-UjWZc4j_sDqjOBGmgdS2zAC_24,186
scipy/optimize/_trustregion_constr/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/canonical_constraint.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/equality_constrained_sqp.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/minimize_trustregion_constr.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/projections.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/qp_subproblem.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/report.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/tr_interior_point.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/canonical_constraint.py,sha256=pYMm2qLrCrvpD7pQZAZpz1H7Zfag3uiwezGBCDx6syk,12928
scipy/optimize/_trustregion_constr/equality_constrained_sqp.py,sha256=dxL7vg49OV8VrKg3HpE7DTYUpSeRVckCCj7t5_Lh9rs,9384
scipy/optimize/_trustregion_constr/minimize_trustregion_constr.py,sha256=GkloTNfoZpPSmyOC7E91BEUU-ffCAaiI53UnwwrzWec,26308
scipy/optimize/_trustregion_constr/projections.py,sha256=-1w4LbwrFnQc8xMhoyiBazu-YNydDqvDcBUMNsu7yx8,13576
scipy/optimize/_trustregion_constr/qp_subproblem.py,sha256=kzhhoadtUgeFmSoBKerKMnSgMSSpzHc_m43hkgqjTlg,23229
scipy/optimize/_trustregion_constr/report.py,sha256=Fvc4n77YuGiRQuGxiDIgkvkgScq5dqko07g7zJOwj-w,1869
scipy/optimize/_trustregion_constr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_trustregion_constr/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_canonical_constraint.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_projections.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_qp_subproblem.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_report.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/test_canonical_constraint.py,sha256=z8MKN6NbaxsH7pj2rbCGdOQ4jXE-eM-SPu_IcNMtIaI,10165
scipy/optimize/_trustregion_constr/tests/test_projections.py,sha256=YQ5iAUYXSTKZusnJxG8yD3Ctyb2qlCPm9IAk8y8pkAY,9048
scipy/optimize/_trustregion_constr/tests/test_qp_subproblem.py,sha256=YJmS5Zewk0mh8qsVCgWQKoDoIoMqOTE_32klYQXK8QY,28368
scipy/optimize/_trustregion_constr/tests/test_report.py,sha256=Clna8yKDSyuFr2cCrtSkdcBjYEb_8J4qRvs7_uUZjQQ,1137
scipy/optimize/_trustregion_constr/tr_interior_point.py,sha256=FU2a3KT4uJ189s4eAxrBmwVh2GlIiSzMN9LFvZqkIBo,14144
scipy/optimize/_trustregion_dogleg.py,sha256=XTvNQtebZTPzl6xGtzHBLDcFD2qe5sSOFOTbmtDWswo,4511
scipy/optimize/_trustregion_exact.py,sha256=H9E0sxJmLMebKgfd5L64h-BxP-WFIdCf6CyvMRUUNjI,15993
scipy/optimize/_trustregion_krylov.py,sha256=OE6ABN1_oeRUUBXWGBA2QFlzxO4GJVBseoNOMB2b4Sw,3095
scipy/optimize/_trustregion_ncg.py,sha256=O2_8p92l_faUHLI26GHK2wV0RTdAR1scli0XhXTTsSo,4706
scipy/optimize/_tstutils.py,sha256=ikvxL4pBR4fy00TDcTdWafpTl8SVcPgdIeIC89LJ2ks,35019
scipy/optimize/_zeros.cp311-win_amd64.dll.a,sha256=_2s8fUw3McCA6mjvIdE2_ZD6_J9MyNtkwxZPuGoneN8,1532
scipy/optimize/_zeros.cp311-win_amd64.pyd,sha256=jP5wQlP98EJFa3HRpPRH2GXkg7FoQCPGZAkxHbfUxnQ,23040
scipy/optimize/_zeros_py.py,sha256=u5E-SHpvmg4y-0on6W5ovbHmsefYoc8u2MNKcnLQPMY,53593
scipy/optimize/cobyla.py,sha256=CHOea_YiOpCNJ5blRI6YqoYieCohU96MNYV-YiWADGc,576
scipy/optimize/cython_optimize.pxd,sha256=FhEeI0aq_Y2P00BpghHKOa7UjhW_uv4e7aEzXdS-QjU,453
scipy/optimize/cython_optimize/__init__.py,sha256=LEyUcZ_tUfRNMYgyFBYUsHfztui15TviJlLCsbZWrDw,5020
scipy/optimize/cython_optimize/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/cython_optimize/_zeros.cp311-win_amd64.dll.a,sha256=sDIqIXuV2q9Yx3CdyRtemxvXGmfMms808HZ0qZ-u0ts,1532
scipy/optimize/cython_optimize/_zeros.cp311-win_amd64.pyd,sha256=5xus22mvmzkYwXh4yNSoziRCU_aMmyzK47kCYAt_9F0,92672
scipy/optimize/cython_optimize/_zeros.pxd,sha256=5o3CUJecdwYcTh6bX9c4O8F04MtmvvW9KluxtFsYDh4,1272
scipy/optimize/cython_optimize/c_zeros.pxd,sha256=kbSds0gdPDP4-oM6U2AaaFhsuEhCDXkzlfZ4BS82sHE,1144
scipy/optimize/lbfgsb.py,sha256=wRXQnUcc8-ePQOMmqVM1D0h-ghuhD_qyW_d35nr6YeA,624
scipy/optimize/linesearch.py,sha256=KiorxXY20ql-mVkDQUdUppTlg_KBES3pCfJHNL8XZwA,553
scipy/optimize/minpack.py,sha256=uxAnCG7Su9Gxi5u8P-BCbAe-qKwkNKtMvIkRoPdrRRk,691
scipy/optimize/minpack2.py,sha256=NTrxn0tvN_vBTHwPu5mXNV74MKj9MSEVt8xQHzpOuWo,531
scipy/optimize/moduleTNC.py,sha256=0BZtj41NEa1nM2XYN9Df-kmG7W1QJG4PWcY40Zour-s,526
scipy/optimize/nonlin.py,sha256=mtj4mAfZhUjTF_12P3dwy3ZWgyteugyqG8oJBKwkhn0,739
scipy/optimize/optimize.py,sha256=IJdwFy_tYftU9lXIecQaBpWWXNsbeWTYuZJ0P5KkYNI,917
scipy/optimize/slsqp.py,sha256=mtkavACQ1Y3Difti8ztWSwdZMA_GCamqGIxw7CXm31k,605
scipy/optimize/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__basinhopping.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__differential_evolution.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__dual_annealing.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__linprog_clean_inputs.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__numdiff.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__remove_redundancy.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__root.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__shgo.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__spectral.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_bracket.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_chandrupatla.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_cobyla.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_cobyqa.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_constraint_conversion.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_constraints.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_cython_optimize.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_differentiable_functions.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_differentiate.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_direct.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_extending.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_hessian_update_strategy.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_isotonic_regression.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_hessinv.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_setulb.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_least_squares.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_linear_assignment.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_linesearch.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_linprog.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_common.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_linear.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_milp.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_minimize_constrained.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_minpack.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_nnls.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_nonlin.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_optimize.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_quadratic_assignment.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_regression.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_slsqp.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_tnc.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_exact.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_krylov.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_zeros.cpython-311.pyc,,
scipy/optimize/tests/_cython_examples/extending.pyx,sha256=hYoYg2ZMzOBFvTBgsEbe8HEcgmRakP-eCTH5wOdZzE0,1357
scipy/optimize/tests/_cython_examples/meson.build,sha256=jAyMIESAHLDCDkdFfJ-kJAOCRtNO6JBD4LdDfE5ZVUU,552
scipy/optimize/tests/test__basinhopping.py,sha256=LygzLaQpSo0ATQQ7zMY2nZmjdPuePuiO-S7O5O8SzVA,19629
scipy/optimize/tests/test__differential_evolution.py,sha256=eCncOJoEBr60xpHmggGS-Nb_5TYB9nmzeaXWo78zblU,71052
scipy/optimize/tests/test__dual_annealing.py,sha256=lPbYomYzCG_OfJE65nXQmKIWm1Gzzsv6m5Umej1x1MQ,16726
scipy/optimize/tests/test__linprog_clean_inputs.py,sha256=8l7kHLqk5mqiRVkv8OLlMQpPXjNJOBWi-jkaWD5n2D0,11988
scipy/optimize/tests/test__numdiff.py,sha256=KBPAsPpz4DwxfbzfvVAgpYNqKWz9MGjlif--0k5RPYs,32166
scipy/optimize/tests/test__remove_redundancy.py,sha256=VC2Tz00TMHan0FJ8QXgKdvazARH58aWCLb97BQZyd08,7027
scipy/optimize/tests/test__root.py,sha256=qpL7_trYXSVfzBwsxIEfrSZNksLT_Ck-2QP1ouDYtzU,4334
scipy/optimize/tests/test__shgo.py,sha256=PDWwQW31T71Zv4I7nas7tcsyxbX8WCb4NQnkUcMglxQ,40927
scipy/optimize/tests/test__spectral.py,sha256=B3d_qUmY55_NiIrAljlKrk4kHDxP8iyb8PVAXFsx_U8,6890
scipy/optimize/tests/test_bracket.py,sha256=13nHpj7GvF7hoC1LxI_mHfF0d88blmSjsYfrcuVjdmg,31363
scipy/optimize/tests/test_chandrupatla.py,sha256=F2r4s9lNaTit8gq3t75tEVO9SBzkXB6eHnffvosSCgs,35497
scipy/optimize/tests/test_cobyla.py,sha256=aDeGZw7EH9DCnnEp40FY-MFM4zk3R5NPprnyW3LTZ_E,5437
scipy/optimize/tests/test_cobyqa.py,sha256=d8gjBa8xeSmY_CYYRhojPqEuFp-0BPDOjmXZMwlelJs,8194
scipy/optimize/tests/test_constraint_conversion.py,sha256=JMsFdtKVt_hhVs_SgTo5wgB0jWw07J6zT0V0995fIXM,12472
scipy/optimize/tests/test_constraints.py,sha256=cn-3BR5et1YJ4FSgvu8bVFsSmLeSqzoVDbcBlR6AvL8,9663
scipy/optimize/tests/test_cython_optimize.py,sha256=XmUrnJrxWKWfH5337FiCAfCV6djffjpQWVg1olG1vg0,2730
scipy/optimize/tests/test_differentiable_functions.py,sha256=oYYhjWOIPjcJkWSNQ4c6KHnW2ElF2w-dnw4ElH73ALg,29230
scipy/optimize/tests/test_differentiate.py,sha256=HUS9NW2NbRdlNwXJ4BOgP5bQyQZT06oqVvSYoNW3-jA,20015
scipy/optimize/tests/test_direct.py,sha256=_JopiGDv5bJeZuALeYyE5a9umkHw21Z-r7yfWbFtdtQ,13470
scipy/optimize/tests/test_extending.py,sha256=IxK7aFgrapdwWunHvCt6ZQnlsdV5k0rHKbbfd8226E8,949
scipy/optimize/tests/test_hessian_update_strategy.py,sha256=8ffce9XMVHif6q7QaTgzFz71mhDbTG_5ftlNskbF9rE,14392
scipy/optimize/tests/test_isotonic_regression.py,sha256=7_lGuoWwYwOdLbUCuezoumQj8haAFMe7RJmK5ztsoOk,7280
scipy/optimize/tests/test_lbfgsb_hessinv.py,sha256=8_Bvj0kpB1mvK3fmh3v_vsYCrH8iFTnmaVjqe1EnSGA,1180
scipy/optimize/tests/test_lbfgsb_setulb.py,sha256=MBgku6a6vtpw8BWoGQNQygMhxYMhMV8vnbpJxXTL8RQ,3727
scipy/optimize/tests/test_least_squares.py,sha256=6vmhGpkGY-Q5T_OlcChTdxGCrfA9x5wH6j2qhsrCsSQ,34899
scipy/optimize/tests/test_linear_assignment.py,sha256=51pamAv4Kf0zrqIUkxk-8iFVF4-iZS6F8CxVH1h0J1A,4201
scipy/optimize/tests/test_linesearch.py,sha256=0y2A8OGW749xhQdw530qfH75GiJ5N-JmY4zKPbtW5-8,11230
scipy/optimize/tests/test_linprog.py,sha256=RPWUCxD5S_y8POKM5AnqGlqo9sXfJyJodlDgJeLd1qQ,100369
scipy/optimize/tests/test_lsq_common.py,sha256=AXdfzIlCO5gQa3gy7Ou9mp1SAHD8-qMP1yCyfK1kaI4,9797
scipy/optimize/tests/test_lsq_linear.py,sha256=GRbHKXgrleNPRLSy9G3Mdw-sr6Hf4X8qdFKzrRckq3w,11176
scipy/optimize/tests/test_milp.py,sha256=0vwenAWdg--VjEJbZrG_agTQARI5v6wXGdEoZvhfaoM,14938
scipy/optimize/tests/test_minimize_constrained.py,sha256=2PbTsjyCbhrvL0AN9_4vggkhP83SQmAhj3OVhGuuZxw,28256
scipy/optimize/tests/test_minpack.py,sha256=QKmwNXhthu-A3M4V7FsuXfYOdF7MGlj5WBsiZ2wAG8w,43296
scipy/optimize/tests/test_nnls.py,sha256=q6oQS6ZpKg-5dkuNK0TkAEgfLB_TKBZw6g7eUTjntOw,19510
scipy/optimize/tests/test_nonlin.py,sha256=YFtOa0TlziAW-xOqe5lxjw370FcNE_dVZ9b2OET-qb0,19027
scipy/optimize/tests/test_optimize.py,sha256=bOtrdAEeMgj6IDykptxdeIZYopYdAskZS5tcWoDqYGs,128051
scipy/optimize/tests/test_quadratic_assignment.py,sha256=C_jLzYepRtwE5iQVbsXJvuzQE6vFzLwSOGWfofP1LSc,16738
scipy/optimize/tests/test_regression.py,sha256=jc-uV89QavAhf34j8J2riNxYY9gIIgOddrRMV5ltC0Y,1117
scipy/optimize/tests/test_slsqp.py,sha256=VwGhdCImOwE3Xq2aTxB5AT4f1tNN4U_Nc0J94q78cdQ,23866
scipy/optimize/tests/test_tnc.py,sha256=XWL1j_u_9ZrkUBOfyFVytIAdD_dw5rRo-0w5IHx_7PM,13045
scipy/optimize/tests/test_trustregion.py,sha256=S57y-AFuek-24XWgUozVMLaoTGySNufU84-nZ8bo-6o,4813
scipy/optimize/tests/test_trustregion_exact.py,sha256=LS8hlLOXrlPv7FpuQuGg7kg7PSlMOZk7ShS_qqtXa2k,13349
scipy/optimize/tests/test_trustregion_krylov.py,sha256=S9GEHBHe_59OEEbR16q0ukOWOGpk6FFqUyBKZDGPgHc,6805
scipy/optimize/tests/test_zeros.py,sha256=44NYWCf258SpbG4X17hbyvqx-hqCcOoUl0PnwZijptQ,36630
scipy/optimize/tnc.py,sha256=znhjH4IV17iV6ghdxwE0aj617iodH6OPPL_Afrj88LU,582
scipy/optimize/zeros.py,sha256=yZg_vt7rR_8z5N2i-Oam3rbMiPdas8l9xgib_sAAThk,646
scipy/signal/__init__.py,sha256=fH2AZexuTrr3INWXnqhNzwRe4fpxr_qUUzzdXFxTSIo,14329
scipy/signal/__pycache__/__init__.cpython-311.pyc,,
scipy/signal/__pycache__/_arraytools.cpython-311.pyc,,
scipy/signal/__pycache__/_bsplines.cpython-311.pyc,,
scipy/signal/__pycache__/_czt.cpython-311.pyc,,
scipy/signal/__pycache__/_filter_design.cpython-311.pyc,,
scipy/signal/__pycache__/_fir_filter_design.cpython-311.pyc,,
scipy/signal/__pycache__/_lti_conversion.cpython-311.pyc,,
scipy/signal/__pycache__/_ltisys.cpython-311.pyc,,
scipy/signal/__pycache__/_max_len_seq.cpython-311.pyc,,
scipy/signal/__pycache__/_peak_finding.cpython-311.pyc,,
scipy/signal/__pycache__/_savitzky_golay.cpython-311.pyc,,
scipy/signal/__pycache__/_short_time_fft.cpython-311.pyc,,
scipy/signal/__pycache__/_signaltools.cpython-311.pyc,,
scipy/signal/__pycache__/_spectral_py.cpython-311.pyc,,
scipy/signal/__pycache__/_upfirdn.cpython-311.pyc,,
scipy/signal/__pycache__/_waveforms.cpython-311.pyc,,
scipy/signal/__pycache__/_wavelets.cpython-311.pyc,,
scipy/signal/__pycache__/bsplines.cpython-311.pyc,,
scipy/signal/__pycache__/filter_design.cpython-311.pyc,,
scipy/signal/__pycache__/fir_filter_design.cpython-311.pyc,,
scipy/signal/__pycache__/lti_conversion.cpython-311.pyc,,
scipy/signal/__pycache__/ltisys.cpython-311.pyc,,
scipy/signal/__pycache__/signaltools.cpython-311.pyc,,
scipy/signal/__pycache__/spectral.cpython-311.pyc,,
scipy/signal/__pycache__/spline.cpython-311.pyc,,
scipy/signal/__pycache__/waveforms.cpython-311.pyc,,
scipy/signal/__pycache__/wavelets.cpython-311.pyc,,
scipy/signal/_arraytools.py,sha256=W7k_0e3AW8Lz3RLPAmi4SXnJMge9N3y1-VZcvetm2jM,8558
scipy/signal/_bsplines.py,sha256=qhxiilrE-SS_ztGnI94eczUB9thXD3w9ZWWr6KZ7aDs,16250
scipy/signal/_czt.py,sha256=dhVmn1ScMOn8rzcvaou8n5s4ik8mlPH7BPsViaSA_so,20020
scipy/signal/_filter_design.py,sha256=9EWZMJCtisUH5GdOj72hS4dFw5fNb8cF6eFRItg7UMc,192240
scipy/signal/_fir_filter_design.py,sha256=PTFO8i2XWMc4RSim0ytWBRfFqrpyoLBdJGOi2XhuiQ4,50722
scipy/signal/_lti_conversion.py,sha256=Cud0nepc1KP8_XHkPwJiHWbNWQFz28bgnkKJlzponR4,16592
scipy/signal/_ltisys.py,sha256=GHZdp69MhMJObK3imskueOCAoQigrLyEUFMfncUhAOM,119804
scipy/signal/_max_len_seq.py,sha256=oVUkL3qSfM1_FDWY_AcUI5fFi2vBQH0f1iXiDVOrIbE,5199
scipy/signal/_max_len_seq_inner.cp311-win_amd64.dll.a,sha256=cm3qJDwQYSP7J9ZKoiG9yU3ET0yjbJqc7SrrpcyRHSk,1676
scipy/signal/_max_len_seq_inner.cp311-win_amd64.pyd,sha256=ef4eCJknzFfAo5vOlAz1NXJXK8xJtuarc1zKyVNqnEM,1006080
scipy/signal/_peak_finding.py,sha256=g7-zEKNdR1CxtAPtP5snOpgSPQTQZag0Q1s-WcCsZZs,50204
scipy/signal/_peak_finding_utils.cp311-win_amd64.dll.a,sha256=pnX8qV5C17Wv3BmmZbuCYsVfFNaNSC1K87iNNZX1VQ0,1688
scipy/signal/_peak_finding_utils.cp311-win_amd64.pyd,sha256=bC-hSbVhKWThOtYr97S5ELdjQK4cD5X0tGPbkYuXugM,272384
scipy/signal/_savitzky_golay.py,sha256=TA5zbmZeExTMJS2ORPe4BJ23PfS7NHKkoOTjRj552z8,13774
scipy/signal/_short_time_fft.py,sha256=3gUdJN9n0llTXqjU_ve-QA_0VApY9HX4azr6peDUDvE,75139
scipy/signal/_signaltools.py,sha256=KbCSs8LJL8dHYn_pMymVfBX8iKuD2_e1bV_4bUQ4dxY,163549
scipy/signal/_sigtools.cp311-win_amd64.dll.a,sha256=kwUE8fcggV-FE2c5IGDEXR84bAiSvflAMgOVDn57hCw,1568
scipy/signal/_sigtools.cp311-win_amd64.pyd,sha256=wD9SJVpC9Un1v0cgHpepqAWHfQ4oswA4mbhQZR0MW18,117760
scipy/signal/_sosfilt.cp311-win_amd64.dll.a,sha256=gwA5nxMTdrX-Rx1pvEjuinqq6gORD8ir6f0JJ1AfI2Y,1560
scipy/signal/_sosfilt.cp311-win_amd64.pyd,sha256=_5g36qQeBOiH41zww9jUBU_uBuCqTpC3tCWNXjgNoTU,280576
scipy/signal/_spectral.cp311-win_amd64.dll.a,sha256=vPtqlRrDTlQ91P_C0WvMVqXzSMeN-pOFCwJU9iXrnnY,1568
scipy/signal/_spectral.cp311-win_amd64.pyd,sha256=-Px8f0KvnnigSqCPN4d1OHGGw0LKff1MT5R_rM8P8fo,1008128
scipy/signal/_spectral_py.py,sha256=uZmMN52Fvw7tXuvzYgJDCt_ExNbWp7y0TImEkUhWS7g,80507
scipy/signal/_spline.cp311-win_amd64.dll.a,sha256=AoNYdnMJ0cDhZrv2HnD2Kv5hJoAckxhzG6Mq_24g7CQ,1544
scipy/signal/_spline.cp311-win_amd64.pyd,sha256=YKdqqSf99abh_LHqXvvaUHyuBI8Az-4eDTR7g0ozZJ4,84480
scipy/signal/_upfirdn.py,sha256=SZeLNVfPAOjmX6Bqjh0xvoUehjYcPFpJ3wwLwqf7RJg,8098
scipy/signal/_upfirdn_apply.cp311-win_amd64.dll.a,sha256=Jtro22ZrgozKE_wBMhhcSKr0YAjBcqUT6nw41TU98fs,1628
scipy/signal/_upfirdn_apply.cp311-win_amd64.pyd,sha256=AKtjF_k3-phwyWN2o5nJE2L91MRfB7PDAU_vDqQLGrA,366080
scipy/signal/_waveforms.py,sha256=SjUjL0zu-cOy1V-xsWxzz3tkwT7ycG4LXlJdQbiuKiY,21195
scipy/signal/_wavelets.py,sha256=Ed42rmbMTF1gDrkvvZj5foxEV73q6XhiijCHJHZ75js,16651
scipy/signal/bsplines.py,sha256=-I8GcUnLC8p9wgK72RW-mo6CmkMlvYvB96fktbO5VBo,666
scipy/signal/filter_design.py,sha256=kXl0f5CqQuqaWbQiJQUfleWWNOdlAexzrLkB6A_v6JA,1127
scipy/signal/fir_filter_design.py,sha256=3A64W0mSoJBgVu-v_KJEIVr7DHDtba4p2PVpClixLQI,660
scipy/signal/lti_conversion.py,sha256=6wCVjM9xgyn3y5YrXT6aPFOUHSISDkNFaSFsVme8BSk,659
scipy/signal/ltisys.py,sha256=hA9Q6qciqgKHRLeUSOedGx9q9jN0dWyERJ3F19_ia_s,894
scipy/signal/signaltools.py,sha256=E06R3as543yhfic9IbAqLRmAwV8G_jMnmPEWRCuXn3o,1079
scipy/signal/spectral.py,sha256=Xf30DdZVzd-sp4GIPvCiyulUJIYNo-x7LxeqlyQLQIE,683
scipy/signal/spline.py,sha256=4xylmC4JyNUioC_v_jsn4fdi2aFMVOy23Id14X8Q9wM,836
scipy/signal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/signal/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/signal/tests/__pycache__/_scipy_spectral_test_shim.cpython-311.pyc,,
scipy/signal/tests/__pycache__/mpsig.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_array_tools.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_bsplines.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_cont2discrete.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_czt.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_dltisys.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_filter_design.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_fir_filter_design.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_ltisys.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_max_len_seq.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_peak_finding.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_result_type.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_savitzky_golay.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_short_time_fft.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_signaltools.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_spectral.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_upfirdn.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_waveforms.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_wavelets.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_windows.cpython-311.pyc,,
scipy/signal/tests/_scipy_spectral_test_shim.py,sha256=mDThCaIAywm55MH8pqan8xUBhNlpYNSwXZpwunLp6K4,20483
scipy/signal/tests/mpsig.py,sha256=3cd_WPgz9dkjalruhXxxAPYCV8Kzs0L8CH4h-0W8jX4,3430
scipy/signal/tests/test_array_tools.py,sha256=GHjEyc4Q-O3PN9C9R1CCQ2Izm-LKGZhg5mX5gZ_FxUk,3743
scipy/signal/tests/test_bsplines.py,sha256=hJ5NvO8Zc1XPiBjrJKfPQ9O2l1W1Px3RFQizEh9uvA8,9041
scipy/signal/tests/test_cont2discrete.py,sha256=1vzkouv0wINTChU3pmQksPPbN9WQ3sbL7l0hahGnOnY,15021
scipy/signal/tests/test_czt.py,sha256=gSrHRP4imVuSnNrYkNrxvb4sdYnAFp6rRUMFr-4C6Qs,7212
scipy/signal/tests/test_dltisys.py,sha256=2ETM-Wwlgc-p6-Vbpu7-Mz1Vp6UK51M8qR2yvb70gOg,22156
scipy/signal/tests/test_filter_design.py,sha256=9jqOA-yqVMas6VJe2vg6YhZ9QOrWQI-IzfDY4YDIW30,200148
scipy/signal/tests/test_fir_filter_design.py,sha256=f5ZmeWmmLR9y4EOerTeqC8FPFi0l7O-IS37G6QqyB9c,28142
scipy/signal/tests/test_ltisys.py,sha256=mEtvF61ENtxS6sfoe2ivNei-mXd-5dnT5iQULHRtQ-A,46429
scipy/signal/tests/test_max_len_seq.py,sha256=8caIaIvvSgV9zsQ8t2MnNBeOSBLqJajPISerank05Qo,3171
scipy/signal/tests/test_peak_finding.py,sha256=wGIxBw9M10mWvrYNtUTGal7HVocplloRf5qRIOuOV-c,34754
scipy/signal/tests/test_result_type.py,sha256=zWsEnxBFDAKmi9IhebffJbvjY9R2aO0kFtUm7skwAm8,1679
scipy/signal/tests/test_savitzky_golay.py,sha256=e7NiioSNnsP2SXYW6xc_ygBPLske5eDIjVf6GjUCIQo,12782
scipy/signal/tests/test_short_time_fft.py,sha256=1dGTYdXRAn3h_CJY7MucN_DovcDoKuPvSZcIRF3Syx4,35314
scipy/signal/tests/test_signaltools.py,sha256=FU8FWDEJNLxponJgGYI8NU-ICKsGie55uqK0V_3sjRk,144135
scipy/signal/tests/test_spectral.py,sha256=UOfgZ8wRFrxrqNBLP13a8yUTXreYGtHK1zu3MS2EYlg,65488
scipy/signal/tests/test_upfirdn.py,sha256=qpJJpoo_Hl0yk7pkuDIAQd6Zgs7CIX5_jE6qhHNICJk,11527
scipy/signal/tests/test_waveforms.py,sha256=j80USvnR7ddMZZeqQ5PeiHbJ5m4E7qHG7QbVD5TxKA4,12326
scipy/signal/tests/test_wavelets.py,sha256=_RGwdTrq5bOJhbBJNTlrcP0HGF5H3utiNv_zTfr4edg,6882
scipy/signal/tests/test_windows.py,sha256=pYFLpJij_6QMdREBieWqCw9KcUORoquAtBgIRPYgfXo,41836
scipy/signal/waveforms.py,sha256=GIzJ60y5sd1PeYWCEHRJeyqb_EKLLKojXjluMM7juvI,619
scipy/signal/wavelets.py,sha256=57ya3NQZXTMKd6c3GdfdmyQZR_dm6WG2EZf13-GatAQ,617
scipy/signal/windows/__init__.py,sha256=G97xwCUcxrUmZO1pb3K_VfzlyUrzcJYpGjMN9UWWC2k,2171
scipy/signal/windows/__pycache__/__init__.cpython-311.pyc,,
scipy/signal/windows/__pycache__/_windows.cpython-311.pyc,,
scipy/signal/windows/__pycache__/windows.cpython-311.pyc,,
scipy/signal/windows/_windows.py,sha256=LGrWA14Bumyj7RZUDRIy_tL1Rqw3nOBk0k4g7oueB7g,85981
scipy/signal/windows/windows.py,sha256=7yfP6swdGZdTI7N37_Gg8PWQz0-HyYqMkfWqAHwiVTg,862
scipy/sparse/__init__.py,sha256=XJGfA_VrFt7sygDaY3zuUxeVlNCZdwEdqLVtntqmSyQ,9564
scipy/sparse/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/__pycache__/_base.cpython-311.pyc,,
scipy/sparse/__pycache__/_bsr.cpython-311.pyc,,
scipy/sparse/__pycache__/_compressed.cpython-311.pyc,,
scipy/sparse/__pycache__/_construct.cpython-311.pyc,,
scipy/sparse/__pycache__/_coo.cpython-311.pyc,,
scipy/sparse/__pycache__/_csc.cpython-311.pyc,,
scipy/sparse/__pycache__/_csr.cpython-311.pyc,,
scipy/sparse/__pycache__/_data.cpython-311.pyc,,
scipy/sparse/__pycache__/_dia.cpython-311.pyc,,
scipy/sparse/__pycache__/_dok.cpython-311.pyc,,
scipy/sparse/__pycache__/_extract.cpython-311.pyc,,
scipy/sparse/__pycache__/_index.cpython-311.pyc,,
scipy/sparse/__pycache__/_lil.cpython-311.pyc,,
scipy/sparse/__pycache__/_matrix.cpython-311.pyc,,
scipy/sparse/__pycache__/_matrix_io.cpython-311.pyc,,
scipy/sparse/__pycache__/_spfuncs.cpython-311.pyc,,
scipy/sparse/__pycache__/_sputils.cpython-311.pyc,,
scipy/sparse/__pycache__/base.cpython-311.pyc,,
scipy/sparse/__pycache__/bsr.cpython-311.pyc,,
scipy/sparse/__pycache__/compressed.cpython-311.pyc,,
scipy/sparse/__pycache__/construct.cpython-311.pyc,,
scipy/sparse/__pycache__/coo.cpython-311.pyc,,
scipy/sparse/__pycache__/csc.cpython-311.pyc,,
scipy/sparse/__pycache__/csr.cpython-311.pyc,,
scipy/sparse/__pycache__/data.cpython-311.pyc,,
scipy/sparse/__pycache__/dia.cpython-311.pyc,,
scipy/sparse/__pycache__/dok.cpython-311.pyc,,
scipy/sparse/__pycache__/extract.cpython-311.pyc,,
scipy/sparse/__pycache__/lil.cpython-311.pyc,,
scipy/sparse/__pycache__/sparsetools.cpython-311.pyc,,
scipy/sparse/__pycache__/spfuncs.cpython-311.pyc,,
scipy/sparse/__pycache__/sputils.cpython-311.pyc,,
scipy/sparse/_base.py,sha256=a4WUTs-tKAHmfFJmKLf_eBx_h-38ZI7zSa1nZfOjRiM,48125
scipy/sparse/_bsr.py,sha256=Ucpd3cD2ypm6sImn_MlHhUBSIIUwxl4K9SvWRNYccRI,31113
scipy/sparse/_compressed.py,sha256=oJm6Ae3SQd5vo91hNgoA1J3gt-3UwY1vi9r05uC9cCU,58596
scipy/sparse/_construct.py,sha256=61IGiHdqb6gu4CzT2d8QHM-GzEYNwlRihJraICr6l-A,49115
scipy/sparse/_coo.py,sha256=xpFp6ky6I4KNbEN5g5RpVUtHRtyZs2hBwbg8Z98Mxnc,32906
scipy/sparse/_csc.py,sha256=FVIItYuIYfSp3MhmVfLSBx48BX2L6xvq7-ZccbNzoto,11421
scipy/sparse/_csparsetools.cp311-win_amd64.dll.a,sha256=ky7sCr2OPT75Ji1HHagm4S_eatIA3Y67NKePN6B8XAU,1616
scipy/sparse/_csparsetools.cp311-win_amd64.pyd,sha256=ZzCnIpVn8fl3-XfNmJEqary0ZRce8AlsW_wB5K5mp24,775168
scipy/sparse/_csr.py,sha256=otsCpnW5wjEc5_4Uz5ZM0PXZyTTJUlSn_52CxPwi1Zc,18595
scipy/sparse/_data.py,sha256=8ARw5fPcQ_x3y-p6-TGyKgUONp7fw0UlG32VRobQXpw,18020
scipy/sparse/_dia.py,sha256=O-660B7HeRqhRINj3RvaGpJu1ZDnoo0je7cGIUCv5Jg,20375
scipy/sparse/_dok.py,sha256=ZUIlgHk4u3QJ7Qt31pJxeQVWXvOL1uT5Dw7UWyYjrSQ,23257
scipy/sparse/_extract.py,sha256=0lSytu6FbfXTRQJs5C2nzEgtxezzEsko8xbgdvMLS4o,5133
scipy/sparse/_index.py,sha256=Il5CqTi56jpP8XUJwV4Lod87c9jf0VsvPpb0momqCao,13671
scipy/sparse/_lil.py,sha256=fE1I_NMYeFFlYyNkiVMgufBN1PCowLuWmtQ_tzr_yes,21024
scipy/sparse/_matrix.py,sha256=AENnr8pqeJM_-i4yCKP_ovO91m-LESoicbDkJQYmfPM,3194
scipy/sparse/_matrix_io.py,sha256=Wr98d8p_VaifuZKm8RNQAFOgHoGmI7DAfr82BxV9uME,6127
scipy/sparse/_sparsetools.cp311-win_amd64.dll.a,sha256=keHXDhVrQAgeYQC5y5J7S9rFCM2k8I66BRe9a__X95k,1608
scipy/sparse/_sparsetools.cp311-win_amd64.pyd,sha256=3bAofIWH9BDrG32-HfOeiZ2wW9dnnCkdx7NT46SeGTw,4189184
scipy/sparse/_spfuncs.py,sha256=vwBEj6xNGh1V17dONJX76wCAj9iFfmteGqh48DZ_vng,2063
scipy/sparse/_sputils.py,sha256=FrGmY1S_V68HH8C8KJGLf8BquMuRBUFvri66HBNjkpg,14996
scipy/sparse/base.py,sha256=JL80rDo0TwYloUa1n4tyLRxb_e6lp2ermCU4f0YkG4k,824
scipy/sparse/bsr.py,sha256=QSzbgv-z06WX8-uPsUnkow_tI2SKMjzbaIp_EWXLmvU,847
scipy/sparse/compressed.py,sha256=wx4UDp1GEYdVvdxZg7HdnAVhCYhfWb1Sg4zZICB9rbo,1052
scipy/sparse/construct.py,sha256=expKWVHfVSTs_809lRGCyJM1zm9urS2cECQdPiqmgAo,969
scipy/sparse/coo.py,sha256=irEGMbdj__vcKOQOcqARXS-exo17SFl7cplcmOOQ1qc,881
scipy/sparse/csc.py,sha256=2zsFtNCw5agBC2g8U-AUtB_PbQfVrPgPhzUSQ2bpA_A,634
scipy/sparse/csgraph/__init__.py,sha256=HZD8a7OrbNi-vyF1-9XaMv9JnYJ5oD2Ns1dX33_9-I8,8055
scipy/sparse/csgraph/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/csgraph/__pycache__/_laplacian.cpython-311.pyc,,
scipy/sparse/csgraph/__pycache__/_validation.cpython-311.pyc,,
scipy/sparse/csgraph/_flow.cp311-win_amd64.dll.a,sha256=7LdJxbBGi4yOPTS7MWSV5iBapFSzG-fVlqatl8-yJFI,1520
scipy/sparse/csgraph/_flow.cp311-win_amd64.pyd,sha256=I4ywla6z0vW7updU0vSqqO0pXntlpu52mjaoRsnLX7w,306688
scipy/sparse/csgraph/_laplacian.py,sha256=3osH21se9ofVO5HY5JDsPkxzOSmi9mXdC6x4RyCOzkU,18771
scipy/sparse/csgraph/_matching.cp311-win_amd64.dll.a,sha256=69RInUNs9zuXmc5_p9cArKlJwJNfpJ2Nj9h1ApKIads,1568
scipy/sparse/csgraph/_matching.cp311-win_amd64.pyd,sha256=bEiHn6qtQHu5sHE8v5o5ycojLL-wb0qsJd0Z5vARZGA,312320
scipy/sparse/csgraph/_min_spanning_tree.cp311-win_amd64.dll.a,sha256=kdJzZgz52gXLas8xebjP29aVvjISvQfPAAyGqaSIOUc,1676
scipy/sparse/csgraph/_min_spanning_tree.cp311-win_amd64.pyd,sha256=-MCDLW7BtvJEoF1DFAqxnoQcFHmWSsY3kzfiVonv4lg,237056
scipy/sparse/csgraph/_reordering.cp311-win_amd64.dll.a,sha256=N33p4SkxNSZifIWjhgjQg2rt8zpnuQDIVUNfXbWEvdQ,1592
scipy/sparse/csgraph/_reordering.cp311-win_amd64.pyd,sha256=Zo5SU71Fgu8wNsL96dxHaPNSQyQ-Z6n-SvimAYCbucQ,300032
scipy/sparse/csgraph/_shortest_path.cp311-win_amd64.dll.a,sha256=bXiaovT2qEfhmPtKiqgk7tCxE7zSe0oIuxZCp8zcMpQ,1628
scipy/sparse/csgraph/_shortest_path.cp311-win_amd64.pyd,sha256=ESJz2t5eXni56Dd1wtT7if8UQxouAptJudnF9Nq5kXk,513024
scipy/sparse/csgraph/_tools.cp311-win_amd64.dll.a,sha256=quSMwK025fouLw3GJOTB3oH0K8jsGxJmY-wX-qJd_kE,1532
scipy/sparse/csgraph/_tools.cp311-win_amd64.pyd,sha256=AGdjaYbb2b8qyKRJlGBhN4Km1-1sQs8iAtleCpTOFVg,174080
scipy/sparse/csgraph/_traversal.cp311-win_amd64.dll.a,sha256=XLEdp-CQHMZZqOXFMUK0LLiXJ9vPCylMZcXHvCPF77Y,1580
scipy/sparse/csgraph/_traversal.cp311-win_amd64.pyd,sha256=tgODAWEYnS7hfloYtDcKxj1N8eHRrrrJKuwYgtOkdjY,611840
scipy/sparse/csgraph/_validation.py,sha256=fmwYnUz1dqo9ronNI3g8RtjxBALgW-6z77cONv8c-hQ,2537
scipy/sparse/csgraph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/csgraph/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_connected_components.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_conversions.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_flow.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_graph_laplacian.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_matching.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_pydata_sparse.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_reordering.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_shortest_path.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_spanning_tree.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_traversal.cpython-311.pyc,,
scipy/sparse/csgraph/tests/test_connected_components.py,sha256=HiW9gS_ttLPBl9XvdVCRZGRKfWdoiW4PsmVTqp1Q-kQ,4067
scipy/sparse/csgraph/tests/test_conversions.py,sha256=s0xD-47azJTCrYl3u1Gb7A6k2frjRnYYKXyomPAH2ug,1917
scipy/sparse/csgraph/tests/test_flow.py,sha256=FDRd335RogmRkkRtH_wUwylXXYr-3WUFwqvktzHLWlM,7621
scipy/sparse/csgraph/tests/test_graph_laplacian.py,sha256=voWlNpjqyE7ksNiPV4abXv-4ZCkn8bdCc2PRAwO52ew,11359
scipy/sparse/csgraph/tests/test_matching.py,sha256=GEoiMXISJlDsyVfWR0LS2fH1YkOpSJzyiyuZWcEbwMk,12238
scipy/sparse/csgraph/tests/test_pydata_sparse.py,sha256=klNb8q050ogW-Sw_ROJt3Ujd8pajnPTFIz34SNOYYJ0,3750
scipy/sparse/csgraph/tests/test_reordering.py,sha256=4YgPFLLffko_c_k1QpqUd2NvXAwIsVbsECnIFNuVHmA,2683
scipy/sparse/csgraph/tests/test_shortest_path.py,sha256=HxAJ8Eo0AdmS9U3caDzCwC5VcUj3-j4QezzLteMAjGk,16376
scipy/sparse/csgraph/tests/test_spanning_tree.py,sha256=HfH-MvJm86KJoeIKAyuhgkzqU5VxvJEamusUAjPeiOg,2234
scipy/sparse/csgraph/tests/test_traversal.py,sha256=6kHqs9jJSzi5qrBGkP5A7LenAI646d-eg1ZoN5eobgU,2921
scipy/sparse/csr.py,sha256=pVwLe3hgplNxZCjDixenMe2_UhHwHnNm_uNTb8X1Ytw,685
scipy/sparse/data.py,sha256=jBxVtluwVoejuNTf-QYgAYw3DdQpJiXM4WfgMUd_SV0,596
scipy/sparse/dia.py,sha256=FI2MAFv_1H0zFa6rrjdE-UZC-TBilpKVdGAspwpL0yY,718
scipy/sparse/dok.py,sha256=GdVpJOh-A6vtT3hR-73ziDU1-V0PFdPVUAfqeTTvUTo,765
scipy/sparse/extract.py,sha256=tcwXkf3fvxKg4PotXMrvrjeMzCgqvUQBbDBzrKdLEJo,590
scipy/sparse/lil.py,sha256=mudAFHN3Tk2eQS1cWTQRAQAXGuPnikOtYWNWYRvwQqs,584
scipy/sparse/linalg/__init__.py,sha256=Tkrzqd0JaFrVJLC1CtQOmIQC37_ceXPTrTQDKCa4xOg,4145
scipy/sparse/linalg/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_expm_multiply.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_interface.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_matfuncs.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_norm.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_onenormest.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_special_sparse_arrays.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_svdp.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/dsolve.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/eigen.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/interface.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/isolve.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/matfuncs.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/__init__.py,sha256=kacCyi9C3cT-m5LKPk8RPUhbjPToaL1dZ9V-UEbJD0c,2062
scipy/sparse/linalg/_dsolve/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/_add_newdocs.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/linsolve.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/_add_newdocs.py,sha256=4NQF7CmyoMBGK9Z_QwULJjgdwiugSyIJXP7Dv-U0XQ0,4065
scipy/sparse/linalg/_dsolve/_superlu.cp311-win_amd64.dll.a,sha256=zz2MXzqByzXeT5IiedO10NSVp_wokdnGL7u5vt1o1aA,1560
scipy/sparse/linalg/_dsolve/_superlu.cp311-win_amd64.pyd,sha256=3r3K_xstNv0BmjIShtTPMCeLeOw26R2oSfRrJR4F6gs,541184
scipy/sparse/linalg/_dsolve/linsolve.py,sha256=4bhvJSeNBF5HFErXDu9HV6TtWSpb_HF2j7QjCFV3-Lk,26578
scipy/sparse/linalg/_dsolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_dsolve/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/tests/__pycache__/test_linsolve.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/tests/test_linsolve.py,sha256=4Byw6cXLqnc1780znvKecgijwUT5jkV-fADc7a0rBLQ,32353
scipy/sparse/linalg/_eigen/__init__.py,sha256=013F3u6pMe0J0wKjMFq_a1jSuO-pwS_h_XzmJI3xfMM,482
scipy/sparse/linalg/_eigen/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds_doc.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/_svds.py,sha256=0T89sPXkULMg8HNjrm9mdLf2-Ndg9tmRZeRIt1goGgA,20799
scipy/sparse/linalg/_eigen/_svds_doc.py,sha256=EoiLm60_nxfHhizy2cG6N93fs5V5g8GV_j-ivhf5Cdk,16005
scipy/sparse/linalg/_eigen/arpack/COPYING,sha256=_LPGx94UYM99CGPDxZluUY64AVouztNpEfPaF4RAs98,1937
scipy/sparse/linalg/_eigen/arpack/__init__.py,sha256=EU0vXuTlZzMcXxCITUTVxUZ_N0EgcDF06Q2rMzy0Q3o,582
scipy/sparse/linalg/_eigen/arpack/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/arpack/__pycache__/arpack.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/arpack/_arpack.cp311-win_amd64.dll.a,sha256=fyhds4nYiSfLF9Mee6NOe-Y_FVFEYleSUOHrOksH-Rw,1544
scipy/sparse/linalg/_eigen/arpack/_arpack.cp311-win_amd64.pyd,sha256=Y2kOVQbO_n4Gtd-FfuT8Ojvs5fukbMGuTvcQuq4FGQ8,920576
scipy/sparse/linalg/_eigen/arpack/arpack.py,sha256=_8BUYPXO5lpi_H7XxDmO87SZYjfV4MQqiDV7DIhacWI,69103
scipy/sparse/linalg/_eigen/arpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/test_arpack.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/test_arpack.py,sha256=Rlm-Fm25WDegCn422wcGCXwx909okcR2cQSVOJYBvHI,24468
scipy/sparse/linalg/_eigen/lobpcg/__init__.py,sha256=8aw6542gPHNriFRBENTZ5rb1M3cqJKToG--paaqpaxM,436
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/lobpcg.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/lobpcg.py,sha256=geZeDgV2DC-EQRMv2ElixIKqFqDSWh6WswrCjVW0VlI,43017
scipy/sparse/linalg/_eigen/lobpcg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/test_lobpcg.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/test_lobpcg.py,sha256=VUy5Aw6w_Lkh5yjnlt7hf4fKSRJROY5R--Mp5hghVEE,24228
scipy/sparse/linalg/_eigen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/tests/__pycache__/test_svds.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/tests/test_svds.py,sha256=g10o3WNw2ycEglgfCdtatiMHngxGj-ODiz0MRSKDEVM,37030
scipy/sparse/linalg/_expm_multiply.py,sha256=eDxFOfSuievrru-l7ktsp5aJqtOatvpqUMac1rowIsg,27122
scipy/sparse/linalg/_interface.py,sha256=ZL3f_IP4pUi7FGRhf6rbkNQeH1xLH3c1Iyc9oZ1ZEZQ,28875
scipy/sparse/linalg/_isolve/__init__.py,sha256=rFcoMYM70Tbtjlc3xhWhgjdNHUOwc4TSV1sOFZwL6Gw,499
scipy/sparse/linalg/_isolve/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/_gcrotmk.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/iterative.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lgmres.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsmr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsqr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/minres.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/tfqmr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/utils.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/_gcrotmk.py,sha256=xSwvQD56zm06b7qkZnKZg6IG-0nVkfl7ygIKZKWmEwo,16240
scipy/sparse/linalg/_isolve/iterative.py,sha256=PGOhmzEwxRA7DK0jrLj5erg1bq9EqUqyitnpnxWS-w8,33205
scipy/sparse/linalg/_isolve/lgmres.py,sha256=kLYy7E6NKplphWDa7pxzJRilLbvpYzOB_sZRaMojwzY,8929
scipy/sparse/linalg/_isolve/lsmr.py,sha256=-TOkfv2dcjZYhk0C0zI7OK3mkV0ytSFil75ngO3t4aI,16139
scipy/sparse/linalg/_isolve/lsqr.py,sha256=6pld_K09HE1YPVYCSMEWfWynJoUR2cqnkHHB1-6hngQ,21801
scipy/sparse/linalg/_isolve/minres.py,sha256=Zux6yZOwYDXjdunybP0yEwhrs3FvwixB-bDb86ukwiY,11263
scipy/sparse/linalg/_isolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_isolve/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_gcrotmk.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_iterative.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lgmres.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsmr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsqr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_minres.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_utils.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/test_gcrotmk.py,sha256=utTSPmJ1OZPN_qEeCezs9FWSAYsh_P1ae53JYAn-3x4,5578
scipy/sparse/linalg/_isolve/tests/test_iterative.py,sha256=8_mqX68-wVtkacG4wYl9yRS1FwJsDQTyDPQPsjaXsBs,26915
scipy/sparse/linalg/_isolve/tests/test_lgmres.py,sha256=JpvwkFXdDyTF5bmQamomfllVJVdEqgMW4RHRfyC4EHs,7275
scipy/sparse/linalg/_isolve/tests/test_lsmr.py,sha256=H6BTOYSAtri-W0OzpJNGDYqIdliXB4egC1qrnDsGaT8,6550
scipy/sparse/linalg/_isolve/tests/test_lsqr.py,sha256=KiB1ndwvfJ1oRE1NezjiQAifBVP8uZIvyhY109P2m0c,3874
scipy/sparse/linalg/_isolve/tests/test_minres.py,sha256=QgErAFy1NTZDB_IjcKx0TZcpH7zmqTCRpt67h3DSg64,2532
scipy/sparse/linalg/_isolve/tests/test_utils.py,sha256=W_ERpPV4ZfxThvgBHxuyhiTBmmfSbQKFFZaSK25mGBg,274
scipy/sparse/linalg/_isolve/tfqmr.py,sha256=otiyTDSQlZSrjshh-frwEhB6isBVYPQev3B6hFya6uU,6419
scipy/sparse/linalg/_isolve/utils.py,sha256=LjktcTFezfc_dtnhxbzOfGB-TZhsjTxx_CDVLDA4MZE,3725
scipy/sparse/linalg/_matfuncs.py,sha256=I7lomLhny43SvgUqjTpmEuWXVBusiWQHyWRNmR4Kw2g,30289
scipy/sparse/linalg/_norm.py,sha256=ORHvQ7Pz-idLk-htCVSvAvlkdJG-ykKKXa-voelRr9A,6260
scipy/sparse/linalg/_onenormest.py,sha256=Ep8GIsh0rx7TlL2azhWfCm_vm6DK5zVtSljwMt2WISE,15953
scipy/sparse/linalg/_propack/_cpropack.cp311-win_amd64.dll.a,sha256=hlIF9cFIPkr0LIiR5XIba6NAN0plcoWzYHES1pZbOGw,1568
scipy/sparse/linalg/_propack/_cpropack.cp311-win_amd64.pyd,sha256=3jOtPfCFhCv5hZHf0pdDfTQMTZfcJYP_7hyuTkWthpQ,603136
scipy/sparse/linalg/_propack/_dpropack.cp311-win_amd64.dll.a,sha256=4hi03Eg0g5ZRBl4O8_zCNaBxlVCuffPv-y9E6HslGkg,1568
scipy/sparse/linalg/_propack/_dpropack.cp311-win_amd64.pyd,sha256=Y7zGSCgcag57lik78lBO-kzKY_mFlMIdXo1vh-L7q1Y,572928
scipy/sparse/linalg/_propack/_spropack.cp311-win_amd64.dll.a,sha256=63T7u-e5hIjv_td1r3N0IPwPp8uujgOLK4NsfsXHz2I,1568
scipy/sparse/linalg/_propack/_spropack.cp311-win_amd64.pyd,sha256=v6pSdvQ0PqZSg4xKR7lcgwZ3yaZxSXvnvRmE_lOTNAQ,574464
scipy/sparse/linalg/_propack/_zpropack.cp311-win_amd64.dll.a,sha256=EO7UwR0R74jiqOO8wNWmL-8QROaZb48zJICopfXlQXE,1568
scipy/sparse/linalg/_propack/_zpropack.cp311-win_amd64.pyd,sha256=2QhNsYkYgoYk9Y_tvMlIXRMa24fd62jDoREkq7ZfvuI,592896
scipy/sparse/linalg/_special_sparse_arrays.py,sha256=0xOXWJwWuQri0DrDiT39w8yni9_suZ0q2fX9z1kNK_U,35160
scipy/sparse/linalg/_svdp.py,sha256=RGMIJrf3u9tiO0HiybGkXovLxVooVa0U25QkImuM6Zk,11730
scipy/sparse/linalg/dsolve.py,sha256=FqCBojSMWKfXKL8U2Kt-uhTQjy2bFRK4jdm-0uvmcLQ,676
scipy/sparse/linalg/eigen.py,sha256=RdD87vomwamZfWiKVcxXX7lgI0on79M2K_jzU9wmr7k,647
scipy/sparse/linalg/interface.py,sha256=pUQ396Go80oD5Nhnw1ORA0ug3ilOEJ00tar6czQ4JVw,593
scipy/sparse/linalg/isolve.py,sha256=ra9gIhO5qfFPMKsW8OgQSf4QepdykZpTUMaXXLakEOY,671
scipy/sparse/linalg/matfuncs.py,sha256=414icIriQbA2Mwro2TI1MMkStV6F1fRi9Hhs9mrpsBk,588
scipy/sparse/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_expm_multiply.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_interface.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_matfuncs.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_norm.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_onenormest.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_propack.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_pydata_sparse.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_special_sparse_arrays.cpython-311.pyc,,
scipy/sparse/linalg/tests/propack_test_data.npz,sha256=v-NNmpI1Pgj0APODcTblU6jpHUQRhpE9ObWb-KYnu6M,600350
scipy/sparse/linalg/tests/test_expm_multiply.py,sha256=whz5vpGzolIL5PrGL9oblNgY1YBYI0GfWqmc-RoUnto,14394
scipy/sparse/linalg/tests/test_interface.py,sha256=AoiAmDNBot5HDtyTWN9Ymjx70MuJtUVZ0av4rvWglw8,18434
scipy/sparse/linalg/tests/test_matfuncs.py,sha256=MrenXiTh_CoJUxgbKfwxs8BW6-dUq32SpNVnYGLuD7A,22333
scipy/sparse/linalg/tests/test_norm.py,sha256=ZuZ0yjU4U5XmkGkWJLPFTdAwF0T-sNUfaHovTuuXcUI,6304
scipy/sparse/linalg/tests/test_onenormest.py,sha256=_1CbXJgWFL-72PYjtlheU27jd5xGX90_Vki834yZ6qk,9479
scipy/sparse/linalg/tests/test_propack.py,sha256=4mkx7pVbS2h9COO0fQsTP2S8wK-0LZYUvAZrmem8mXU,5724
scipy/sparse/linalg/tests/test_pydata_sparse.py,sha256=DMlHQ-uhYiTaz5uV7sYlRjhRLThM9qHs9ItNCGtJOaE,6488
scipy/sparse/linalg/tests/test_special_sparse_arrays.py,sha256=a3CKlj_vI_A45sZgx-36usRCswvQNeutrX0hRK9bkzk,13191
scipy/sparse/sparsetools.py,sha256=sKV6diNaH4r4B1BV-F-1AvuGAHw2IpyraSJlBoXZ9YY,533
scipy/sparse/spfuncs.py,sha256=FK1cecfhjHLk2_gSAoTSsved5HgcOwUPrtNSnG7f2_o,525
scipy/sparse/sputils.py,sha256=ckuUej4jR09MrvynJTZ9c6_QCKTHn-7ZUeBcBf6MUzs,525
scipy/sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_arithmetic1d.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_array_api.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_base.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_common1d.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_construct.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_coo.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_csc.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_csr.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_dok.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_extract.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_matrix_io.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_minmax1d.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_sparsetools.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_spfuncs.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_sputils.cpython-311.pyc,,
scipy/sparse/tests/data/csc_py2.npz,sha256=usJ_Gj6x_dEC2uObfdYc6D6C8JY4jjROFChQcZhNAfo,846
scipy/sparse/tests/data/csc_py3.npz,sha256=axuEMVxwd0F-cgUS0IalpiF8KHW4GNJ3BK6bcjfGnf4,851
scipy/sparse/tests/test_arithmetic1d.py,sha256=uBDTwReklS7TR2AIZPFvniTcA-7-N0nZvaSNEwiIvD0,12213
scipy/sparse/tests/test_array_api.py,sha256=UZxeuYpbYQI2Qkv9NIP18uPTKxpXsMz2ey5Zp0D13h8,15089
scipy/sparse/tests/test_base.py,sha256=0c2vB5PfMGW3RHCM86EQM8CeNZ87Bol2H-DZcU4aD9k,195655
scipy/sparse/tests/test_common1d.py,sha256=XnuJM9QRbssgbAw2EzBtP1yP3_WawBwHv_Fr-DycBLo,15927
scipy/sparse/tests/test_construct.py,sha256=MkyfXuCgsgqW1UJKx_M44hvxNKYh-UpjkYAVrzbcjis,37385
scipy/sparse/tests/test_coo.py,sha256=pp5RbOaygceLGxBrfIHO8nAyhh8z3Yp8AWi1dwLaS2c,8425
scipy/sparse/tests/test_csc.py,sha256=ak1_Ka7itovqPa9NltnRgnMx_yvjNNw6sFsPaISZZyU,3056
scipy/sparse/tests/test_csr.py,sha256=YgflHiyhn21bOaIsh3EDTktGAvLFqwa-9zZ8-gA2tA4,6747
scipy/sparse/tests/test_dok.py,sha256=qZSf_xppksWXr37XimNxqzeR8d_xs2lMiblPpy-gloA,6258
scipy/sparse/tests/test_extract.py,sha256=NJIEcflkpb-k0llvH_6uJMAoaysWliWyi4bRjl0NhN8,1736
scipy/sparse/tests/test_matrix_io.py,sha256=7SOFHH8giia5Xdm5QmNuu1Sr5rLYtGF916iQYPqIsJU,3414
scipy/sparse/tests/test_minmax1d.py,sha256=Ceacg9C5H_qMXEsiLrxpwzUeDUyhivEYCKlNGANwk7Y,4397
scipy/sparse/tests/test_sparsetools.py,sha256=5qjHLoIkDjWyiSTJQ0aXK9srBn9ysK2rJkGny5o1FZs,10882
scipy/sparse/tests/test_spfuncs.py,sha256=jUPhJjGat0YjcEeZWrD6Iz2G3el0ubAyts4ezvbrCM8,3355
scipy/sparse/tests/test_sputils.py,sha256=ddNCfkxAd75AnzYQNFCqfNY3yvNWEWLJD4II2T8YOqo,7482
scipy/spatial/__init__.py,sha256=R-KvSAgfE2qCcRt2wtRTFMlkdaoniXqXloDLaayMyLs,3826
scipy/spatial/__pycache__/__init__.cpython-311.pyc,,
scipy/spatial/__pycache__/_geometric_slerp.cpython-311.pyc,,
scipy/spatial/__pycache__/_kdtree.cpython-311.pyc,,
scipy/spatial/__pycache__/_plotutils.cpython-311.pyc,,
scipy/spatial/__pycache__/_procrustes.cpython-311.pyc,,
scipy/spatial/__pycache__/_spherical_voronoi.cpython-311.pyc,,
scipy/spatial/__pycache__/ckdtree.cpython-311.pyc,,
scipy/spatial/__pycache__/distance.cpython-311.pyc,,
scipy/spatial/__pycache__/kdtree.cpython-311.pyc,,
scipy/spatial/__pycache__/qhull.cpython-311.pyc,,
scipy/spatial/_ckdtree.cp311-win_amd64.dll.a,sha256=V0Jr8mscA7oGNvQwnWTI5hCWDxnCVweW3BH1Q28-BfA,1560
scipy/spatial/_ckdtree.cp311-win_amd64.pyd,sha256=OCkRmQiGmYSmM47Fx63TwarWhgDghewtsDS-2Pu5eck,1697792
scipy/spatial/_ckdtree.pyi,sha256=9FSHN9c9NdzQnUMkGIeUTqXQgS9W5HjStL8T1ifWCv8,6208
scipy/spatial/_distance_pybind.cp311-win_amd64.dll.a,sha256=frVr7AKk7DJRnjx4yz7E-WuMSGoQmNIfEBTyyBFCHZk,1656
scipy/spatial/_distance_pybind.cp311-win_amd64.pyd,sha256=qIy-9jyuPkKfniVp8dvyivliYiF6D-f5WoKKmOWnDDg,1374208
scipy/spatial/_distance_wrap.cp311-win_amd64.dll.a,sha256=fF_kiS6kzPOLOvH5grT4dYJYfkDMQ3dC1wmVBW30qQw,1628
scipy/spatial/_distance_wrap.cp311-win_amd64.pyd,sha256=VbKuDF2d3VrvweQyS1NjGWZ9LysfvvJ0UI6XgQVwZgI,111104
scipy/spatial/_geometric_slerp.py,sha256=BYxiz6U5lSov5Y238WxGbf51yEycmM3HjGgGvOhARVI,8221
scipy/spatial/_hausdorff.cp311-win_amd64.dll.a,sha256=bfuqHw4l4vrisqCsQjOD6a2Cv8pSyDzcP9L7qDB5fqQ,1580
scipy/spatial/_hausdorff.cp311-win_amd64.pyd,sha256=FJuhB7NsJOW_na_NWv5FECvLznWvdaZmgaQQfcDwdAI,226816
scipy/spatial/_kdtree.py,sha256=zVE9nZP-rDaMFfSMiATExFwGHz7TMYmhuI_xQwX933w,34363
scipy/spatial/_plotutils.py,sha256=DPj2t9Jrs-ujsAeT9IxpxEAPbny1QNfr-GG9rvPNGlg,7529
scipy/spatial/_procrustes.py,sha256=5k0L3ausfrPyOjQFckcGa9_2BNxZp7E-6R7EY6jNCLE,4561
scipy/spatial/_qhull.cp311-win_amd64.dll.a,sha256=cOGSc2kfGm0CaCU76eOqlvjoXGpHRp_xetaMz-7FYiM,1532
scipy/spatial/_qhull.cp311-win_amd64.pyd,sha256=lyJkHOkvohTp_4OzVPTpAj9dDwOeQGztZx8zbOWRJuY,1080832
scipy/spatial/_qhull.pyi,sha256=L06jd8pQcSB-DcRMJe8wq0XvlIEzuOrZQAffq4zD7xU,6182
scipy/spatial/_spherical_voronoi.py,sha256=4qbm4MAsoy-DcllT4erfjLgFLGVQL0X8iBKpZJKUaNE,13880
scipy/spatial/_voronoi.cp311-win_amd64.dll.a,sha256=dIzQFLBDOoj_miwDfLtMlNTXmAR3vINt2NwbnWZN6tg,1560
scipy/spatial/_voronoi.cp311-win_amd64.pyd,sha256=-ZlEtFGjmrZr8M_UrmteB606LjAw5bgyKERW39OmBiI,218624
scipy/spatial/_voronoi.pyi,sha256=gaEmdjWgHeIA9-D6tYXwimJCppJzgc6yc-sfLju9Vyc,130
scipy/spatial/ckdtree.py,sha256=Opsyz6WpYiV-27W5GJdfOWQTkQToWndreEx4GnwT2qM,541
scipy/spatial/distance.py,sha256=FqDt0LwIRoVBna1hClg2R2-BM1Sg7pXIsWCyklPKroE,94476
scipy/spatial/distance.pyi,sha256=AvjPPnJxnoDRusxs6Mh-UgAdqLW3yJtdje7lZelWSk0,5484
scipy/spatial/kdtree.py,sha256=zc4Kg-a7EvN2hXJq6HuCdPl6ccEbg5GyDV67Xq5nsiA,661
scipy/spatial/qhull.py,sha256=10n1eDcF9qrUmGpqrEb8sLjw_cXfSSgT_WU0-z4ucU4,647
scipy/spatial/qhull_src/COPYING.txt,sha256=liRS5zfffHQ6PcJ0QjIHECi4wEUOdnSlUFfDMOoZd-s,1673
scipy/spatial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test__plotutils.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test__procrustes.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_distance.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_hausdorff.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_kdtree.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_qhull.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_slerp.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_spherical_voronoi.cpython-311.pyc,,
scipy/spatial/tests/data/cdist-X1.txt,sha256=LTxfJD5Hzxi0mmMzhzCgp0dUJflrg6O5duj8LVxa9lA,5760
scipy/spatial/tests/data/cdist-X2.txt,sha256=Tb2q8iKbPvUlxO_n1pBfYkWvu1z_LuBcwFXCIRZgBOg,11520
scipy/spatial/tests/data/degenerate_pointset.npz,sha256=BIq8Hd2SS_LU0fIWAVVS7ZQx-emVRvvzgnaO2lh4gXU,22548
scipy/spatial/tests/data/iris.txt,sha256=sWic54AuDE3IyLk6XOw0BcyCSzr5BkXrpDJc7B00OTQ,15150
scipy/spatial/tests/data/pdist-boolean-inp.txt,sha256=D6kMC8OK5H2Z6G07l592VtEENnehwy8wuM1rjVq7wXU,50020
scipy/spatial/tests/data/pdist-chebyshev-ml-iris.txt,sha256=Ysw196_xQ_ApwKiaB7OXZ-gVvhvhFH8wUZAw7EfacTs,178802
scipy/spatial/tests/data/pdist-chebyshev-ml.txt,sha256=jtPmZwg5AfpIom-wMjty8_6tI69ePUnJ7eYdFyFq9G4,3042
scipy/spatial/tests/data/pdist-cityblock-ml-iris.txt,sha256=Wf3dVmhan8EEYiKAsrgN5WNMmAmH9v9NjJpO4xRdmPE,178802
scipy/spatial/tests/data/pdist-cityblock-ml.txt,sha256=aMGSV28cjKIV2CRfdR2zZsisln6oxi1tYQbxZ9tuMMo,3042
scipy/spatial/tests/data/pdist-correlation-ml-iris.txt,sha256=PdVB_30O5CHL9ms8KRB1kFiswp9Sed8CV540WQvNaJc,178802
scipy/spatial/tests/data/pdist-correlation-ml.txt,sha256=VDgXqx3vUn26r1EZFcSk-rq1-wef2WVFcoBWLG8zvro,3042
scipy/spatial/tests/data/pdist-cosine-ml-iris.txt,sha256=q5nrvDyyqdltrjBRyT5eFVQIW-y2Igb2JdC8tBAXl7E,178802
scipy/spatial/tests/data/pdist-cosine-ml.txt,sha256=nLK5yDtcmU-svoYeLPuZZoolZpxmpOaeLswLLR5QoGM,3042
scipy/spatial/tests/data/pdist-double-inp.txt,sha256=2UzMIvz_r7ZT2Twt-jH-pst0Avue4ueGZezk7T2_6q0,50020
scipy/spatial/tests/data/pdist-euclidean-ml-iris.txt,sha256=VWMmzL2jP_vhDzTBhYtZH7-VBKJFpafRROrefDeokWI,178802
scipy/spatial/tests/data/pdist-euclidean-ml.txt,sha256=Kn8vHq17IbWtj1G9XzCFY1EuXFTPnD7gX9FwW2KSm44,3042
scipy/spatial/tests/data/pdist-hamming-ml.txt,sha256=OqjHwrqGBwmwsfTeN2fI1EFiWSpOA6ZlZYwN121Ys1o,3042
scipy/spatial/tests/data/pdist-jaccard-ml.txt,sha256=nj_JYQ6bfvhlFDKu5OC_zBeOzPKgewkmpxScJEPAzhs,3042
scipy/spatial/tests/data/pdist-jensenshannon-ml-iris.txt,sha256=wz1O-EHZT2cDwFtow4ZoLWQgS8WY6DPzb_Ik9SXA8Fs,172739
scipy/spatial/tests/data/pdist-jensenshannon-ml.txt,sha256=rps5AwUK4Up9qCR3wAE1wfwuKE5ftKZTHChPYH0hsfA,2819
scipy/spatial/tests/data/pdist-minkowski-3.2-ml-iris.txt,sha256=rfWuQ4RgSfY0ko_jqlSTjQIJYGCHeEbEwjHYpAC9Ivo,178802
scipy/spatial/tests/data/pdist-minkowski-3.2-ml.txt,sha256=jKlA87ldGof8ndZLkV9l97-Tsh2uXTIlyZRsMoWr0gA,3042
scipy/spatial/tests/data/pdist-minkowski-5.8-ml-iris.txt,sha256=bWYNT7XqpLTGkQBMfnCFJ7bnoxooZlzfI0_O54UOquI,178802
scipy/spatial/tests/data/pdist-seuclidean-ml-iris.txt,sha256=bGIB7ygJC8CGHAoVKtwOdPgAqDM5bzffpgxYMTCKgmQ,178802
scipy/spatial/tests/data/pdist-seuclidean-ml.txt,sha256=g9-qvSA4qe4S0xPxnTa17zqa4Z-P5S0TV1gAsbr-RAs,3042
scipy/spatial/tests/data/pdist-spearman-ml.txt,sha256=vpgBfMlrUUH-nYlnLqm_Ms-MIjrgOlNCW-TW8C4Yan0,3042
scipy/spatial/tests/data/random-bool-data.txt,sha256=sydRG9aL6CH9i7-Nt5X2mNyQDsu9OC1fbUD-MRdt5bc,6100
scipy/spatial/tests/data/random-double-data.txt,sha256=OcmsKJSbi_eY65ld50iAatodSzw2NxFfDJIDHx0lOpQ,75100
scipy/spatial/tests/data/random-int-data.txt,sha256=saHYkK0CMRzV78RV18tUGIF4uKQlOqkN4cEWqD4gnFw,10366
scipy/spatial/tests/data/random-uint-data.txt,sha256=aBbEt1tldXIvAvkDMbSr9WddkxXtMTa05Qq8j4s55CE,8811
scipy/spatial/tests/data/selfdual-4d-polytope.txt,sha256=x-c_sDM8alTwdaj7vYvGAQEsjrg6n5LycGrrPR4dlY0,507
scipy/spatial/tests/test__plotutils.py,sha256=n9OCDDgipsHJy2le-P-8fcS6PD8IQ33zmiz2ZAzbslY,3905
scipy/spatial/tests/test__procrustes.py,sha256=iZypw3jevzOPiHv15UZHwc3sXE3pNlUlCeKTZfoL5vE,5090
scipy/spatial/tests/test_distance.py,sha256=EmXa5NaV7a7r0oI8kLhBrJEPgriSq_H3BCi7abcn__0,86420
scipy/spatial/tests/test_hausdorff.py,sha256=7ZZJjse3SoM8wYx_roN7d2nYIJwHAh_QbXD2ZqZxEjE,7286
scipy/spatial/tests/test_kdtree.py,sha256=7EwTIS7OR9ZnsZrOKxuYKcsPPcazec0s8K2CyInS1_4,50857
scipy/spatial/tests/test_qhull.py,sha256=pZMEmpm0jHQiF2lyDxpHkE3jZanTZ6rzUZO-bzrPm3A,45621
scipy/spatial/tests/test_slerp.py,sha256=DDZie6nQFvdtM8PAY26gnUluKQALFUPfudI-WQbP1cA,16812
scipy/spatial/tests/test_spherical_voronoi.py,sha256=nNxAPYBP0fzY5WAsGLBO3PHMIJ7UeIBqjY1_nAqrFdI,14850
scipy/spatial/transform/__init__.py,sha256=AR19SJ8oEv3Pt2eXGRiHkglp7wU_rGvRJ8JEIBqs4AI,729
scipy/spatial/transform/__pycache__/__init__.cpython-311.pyc,,
scipy/spatial/transform/__pycache__/_rotation_groups.cpython-311.pyc,,
scipy/spatial/transform/__pycache__/_rotation_spline.cpython-311.pyc,,
scipy/spatial/transform/__pycache__/rotation.cpython-311.pyc,,
scipy/spatial/transform/_rotation.cp311-win_amd64.dll.a,sha256=bHPiuZJRZDpSRrfiz1Lwy7d-1aO18qYUn5J2umb-zJ0,1568
scipy/spatial/transform/_rotation.cp311-win_amd64.pyd,sha256=41VVhfcG-g_1ibLh25sLPTnw8a7MtwkOdr2tRchpAtY,918528
scipy/spatial/transform/_rotation.pyi,sha256=8Fw3_XwHGLl9a3jqPkKV3AVGeHnr8Kja7sYPBSGUmKE,3209
scipy/spatial/transform/_rotation_groups.py,sha256=XBEazTsMg71VeDaexivk9VYmVDgHPkNji9FrlRypjyc,4562
scipy/spatial/transform/_rotation_spline.py,sha256=bQlcYfmYrF1-__9YFv_Utkr-5yLRwlsXJcS-y6eENDs,14543
scipy/spatial/transform/rotation.py,sha256=ZpBp9OghIvAzY601Zb_9TawwzxRgS9ydRR5aNcboekg,577
scipy/spatial/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/transform/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation.cpython-311.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_groups.cpython-311.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_spline.cpython-311.pyc,,
scipy/spatial/transform/tests/test_rotation.py,sha256=VNKkU5SF1dErAFym1Am_UdKxDmzEWSjPgCROrZxQV8Y,65840
scipy/spatial/transform/tests/test_rotation_groups.py,sha256=mATjBt62JCkoqpEMHU2zXB9HF-h7KFpWMBrSLyxnkTA,5729
scipy/spatial/transform/tests/test_rotation_spline.py,sha256=yMBr91x2Tt2GqMvJVTOMcO2n3xgOwGC80MN7HNXQkdM,5267
scipy/special.pxd,sha256=vZV_tS467FzwvnjYCtD4T8r5NAuPmITjU8ccJioLZ3I,43
scipy/special/__init__.py,sha256=FKi05OxjItayyrlMUYgPlzMFAcJQzc2bMa9luxLC0C0,34183
scipy/special/__pycache__/__init__.cpython-311.pyc,,
scipy/special/__pycache__/_add_newdocs.cpython-311.pyc,,
scipy/special/__pycache__/_basic.cpython-311.pyc,,
scipy/special/__pycache__/_ellip_harm.cpython-311.pyc,,
scipy/special/__pycache__/_lambertw.cpython-311.pyc,,
scipy/special/__pycache__/_logsumexp.cpython-311.pyc,,
scipy/special/__pycache__/_mptestutils.cpython-311.pyc,,
scipy/special/__pycache__/_orthogonal.cpython-311.pyc,,
scipy/special/__pycache__/_sf_error.cpython-311.pyc,,
scipy/special/__pycache__/_spfun_stats.cpython-311.pyc,,
scipy/special/__pycache__/_spherical_bessel.cpython-311.pyc,,
scipy/special/__pycache__/_support_alternative_backends.cpython-311.pyc,,
scipy/special/__pycache__/_testutils.cpython-311.pyc,,
scipy/special/__pycache__/add_newdocs.cpython-311.pyc,,
scipy/special/__pycache__/basic.cpython-311.pyc,,
scipy/special/__pycache__/orthogonal.cpython-311.pyc,,
scipy/special/__pycache__/sf_error.cpython-311.pyc,,
scipy/special/__pycache__/specfun.cpython-311.pyc,,
scipy/special/__pycache__/spfun_stats.cpython-311.pyc,,
scipy/special/_add_newdocs.py,sha256=CA1z4iHttfRQy-atRpv6WiScb6Y0EH8SPmVpRku8vQk,359131
scipy/special/_basic.py,sha256=iBF93V5c5rvlK7cGg3ra8KpVJra6xCal2eCOpbqWJqA,109027
scipy/special/_comb.cp311-win_amd64.dll.a,sha256=kSJBX7VwGaOfAVejvdWc8xQkh0vcbdIMqQgZLB0SJMQ,1520
scipy/special/_comb.cp311-win_amd64.pyd,sha256=jwQA_deZXG7N4pGfyImyLlH-B-owBvxcCeRWOd4vqT0,49152
scipy/special/_ellip_harm.py,sha256=Km_A9XgXnYTleTtRuUzOYLxR8OEUtmiYJLYsRSJaSNI,5596
scipy/special/_ellip_harm_2.cp311-win_amd64.dll.a,sha256=rgd9pTViOUwNbnxdDCp_Mbuoin3Cm7c8p654NSQQMZM,1616
scipy/special/_ellip_harm_2.cp311-win_amd64.pyd,sha256=IP3P83iql30xzGB_BhxcD93fmjIPVxOmfx1xSw3FPoQ,109568
scipy/special/_gufuncs.cp311-win_amd64.dll.a,sha256=s-R1rwzyClCi1nOSa8Q9JMLOt0cNFPSVO0XWSNT9gBA,1560
scipy/special/_gufuncs.cp311-win_amd64.pyd,sha256=vwmPLPA2onCA58LpKt4b8cirrmruxJb_Jbgiodfty_A,337408
scipy/special/_lambertw.py,sha256=IYmy0Ymjk-l7T84uHr8_OADgpsUPy1K4F17QGRwWXuE,4111
scipy/special/_logsumexp.py,sha256=dyubvAoaG4na7SRGD4EIsiFv3EOgxwemGpDfeYDf5Y8,9415
scipy/special/_mptestutils.py,sha256=L5x7loWaUvCVsexqUH130bhphXN9DH9OTFHcIWu2tMo,14894
scipy/special/_orthogonal.py,sha256=jlqsJK2xVS_RElIVwA22-K61JPBBx2zEarYq0GjyVic,77163
scipy/special/_orthogonal.pyi,sha256=BLwCAOBK4AAO2GA6APGEClZ9mIOUjHS76K3pVSxtF2U,8608
scipy/special/_precompute/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/_precompute/__pycache__/__init__.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/cosine_cdf.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/expn_asy.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/gammainc_asy.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/gammainc_data.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/hyp2f1_data.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/lambertw.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/loggamma.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/struve_convergence.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/utils.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel_data.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/wrightomega.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/zetac.cpython-311.pyc,,
scipy/special/_precompute/cosine_cdf.py,sha256=7Fjlx721WrFcUMSUh6Vv40NMiR1QX71963X1tHOd55M,371
scipy/special/_precompute/expn_asy.py,sha256=trfFT7O7EweO7DfQHIHCzkwWNuqm7sTtCo-HjsUQthY,1319
scipy/special/_precompute/gammainc_asy.py,sha256=8XBo6RBUzdxUhH7GrVeVTzsMJgSnGb9NpknFAdHwcBY,2618
scipy/special/_precompute/gammainc_data.py,sha256=w3vsqOEvC6a7s58voRucZdWFXpO3jIfSGrGwVTNEalY,4201
scipy/special/_precompute/hyp2f1_data.py,sha256=rK8obG72p-UmpdjSvhS6-xm4A4TRscsUNAMKOuKZW-Q,15191
scipy/special/_precompute/lambertw.py,sha256=UsSir6v9vfyYrlu5F3W-Qn3MMW5RzqqftfZAC-kTH8I,2029
scipy/special/_precompute/loggamma.py,sha256=J2jz62nBO-7JEOA7nG4JQQt5BuXSfUPRik2r3pwTr_s,1137
scipy/special/_precompute/struve_convergence.py,sha256=f21NuMoJE1Di4MF2kZfd6b_wiwqml-uQPylWdpncK_Q,3755
scipy/special/_precompute/utils.py,sha256=tJpjcNS6WaXzqP7cLac7yFyXfb_pfe3AKvDCa0mwi9A,925
scipy/special/_precompute/wright_bessel.py,sha256=4IHODoYYrAFKw8nlJWAPzrOduiHIMUILTJaqtA4jbq4,13210
scipy/special/_precompute/wright_bessel_data.py,sha256=TIwEyxKLTl2DqbLvppxdVWB1jJu-hL157M3JMjFK6eI,5799
scipy/special/_precompute/wrightomega.py,sha256=hAKXz3Kk9L-5JOBA86U6-yC_tsF8gOz0p8Y1vmWcAzE,996
scipy/special/_precompute/zetac.py,sha256=cZMVkubEXtildiIM9p5ldrV1sEoy240MckKPO3JhM6A,618
scipy/special/_sf_error.py,sha256=hDbymj1eSiPjl828PR-Kidkcd8RiVDpJEB-fGA9ltHM,390
scipy/special/_specfun.cp311-win_amd64.dll.a,sha256=JEQwYHFHqXcFyTYdnxrHgYB_B9DrMdl6-gkCz_IK8m0,1560
scipy/special/_specfun.cp311-win_amd64.pyd,sha256=WlG0bgH27g0w4nr6rNbB_m0rACzTRKFafsklb21WIuE,202240
scipy/special/_special_ufuncs.cp311-win_amd64.dll.a,sha256=xSVqbmvBKc9-UTh-thbmHqMyvLt8nkMgl6hIpxCsPDA,1640
scipy/special/_special_ufuncs.cp311-win_amd64.pyd,sha256=-wUKXWgS-eQiuzqkn8KattaNY6-87rjWvKGnjff9O64,1050624
scipy/special/_spfun_stats.py,sha256=Zb72pfmi5hzvkRXD2QXhVnJMmYyIjoqrg6tVxaCcoSM,3885
scipy/special/_spherical_bessel.py,sha256=o3RiJ46ravdMI1pF0HOLhahs8RLf-63WlUXNCi628jU,10774
scipy/special/_support_alternative_backends.py,sha256=_KXWukvYWFEUI5RMKjh9BOE6i43dEQD2RMS30_Aw_lQ,4537
scipy/special/_test_internal.cp311-win_amd64.dll.a,sha256=YIG9krbIIv4h5mdI9eSA_6up7eaCPgLCvNzwxtXiSls,1628
scipy/special/_test_internal.cp311-win_amd64.pyd,sha256=OTTmnIFvP1AtHdAxJDYUXsI8dNSex8t-i3J9auSgdZQ,233984
scipy/special/_test_internal.pyi,sha256=SLu2iMKH7AWdVCbKlF0SpGFEdu2i-opkrFoCTq6zMaY,347
scipy/special/_testutils.py,sha256=_yhL5TN4pQxabMRt9NLqoOh6hWI3GuX4MzkShaNA084,12348
scipy/special/_ufuncs.cp311-win_amd64.dll.a,sha256=iEJskIhYPZKZzCBM_DTY-YMTAB8GWxuCVNMZI_T2Gps,1544
scipy/special/_ufuncs.cp311-win_amd64.pyd,sha256=ZVQilohO6-59Fsx1SjtmmsTET7mRpNT8_Mm-6RgC-Xw,1538048
scipy/special/_ufuncs.pyi,sha256=7peMWBnLQLL6-EwrwnktPrBmKvphhpIghWGiLIK2XM0,9430
scipy/special/_ufuncs.pyx,sha256=lze679F-AJnOsqXwRYyVmVD8A1knm7L9v-W0oww7eT4,728326
scipy/special/_ufuncs_cxx.cp311-win_amd64.dll.a,sha256=FOSKQNte71hLZ--AgH9ny8OahZjZF5Cj2c7_LTKW0YA,1592
scipy/special/_ufuncs_cxx.cp311-win_amd64.pyd,sha256=vmSbQaqWU8O1z53E6rgcHaCMpT9bw3yjk8pTqKUd1bw,2310144
scipy/special/_ufuncs_cxx.pxd,sha256=9kdfK5zs7frF9j-tzjvgP1xRXulgwPFlQ_LodHNk2GE,5206
scipy/special/_ufuncs_cxx.pyx,sha256=4TdOR6EIhyg3bxdl8xNg1NaHkLpHx3Z82E4sX-iDWcY,28639
scipy/special/_ufuncs_cxx_defs.h,sha256=73W6ewi3L8SXMC6vpfUdiuma1ihtEJ20h5jbfyoDcaA,8746
scipy/special/_ufuncs_defs.h,sha256=fJqhIERMjFg7ffB53fp_3a2UqS-t0JP9uLMe03YGohU,5014
scipy/special/add_newdocs.py,sha256=5i4vyh9UrnCUZ6zTcVmy0YhsbP6z1Mddxb7qrUdWwKE,451
scipy/special/basic.py,sha256=UVJGOHZCijP6H5Kiexi5U2R-CVqfUSCo4Rhv6sxb4bU,1669
scipy/special/cython_special.cp311-win_amd64.dll.a,sha256=Xga3v2cxPMMzFri89ovRa91U5-M1qwzzf4lpuhzHlCM,1628
scipy/special/cython_special.cp311-win_amd64.pyd,sha256=fdD4ye-XP1mS-k1rqZZCmsm7jiHcgmbbSLBunaCKDmc,3201024
scipy/special/cython_special.pxd,sha256=TFQT1NjX0kY0paR0ojg01W-xNgNx3jKE6Zw8WUVOU_Y,16686
scipy/special/cython_special.pyi,sha256=sC6nE-1ffPYfFKVvopszCin-ilDqBWkDW2O6s0and2M,61
scipy/special/libsf_error_state.dll,sha256=n-aOdxik8NGsTJ4O6ryXojGfhqWhFHmRndaQKz4xx1A,112594
scipy/special/libsf_error_state.dll.a,sha256=BioVWE11owmXmF4E65cxaOqSODSphN5mDLBkNsPgiTM,2232
scipy/special/orthogonal.py,sha256=YlmIcqtx79IX4UoUK7FgKuyOLfFCIXCYeFHAvSz4HiM,1769
scipy/special/sf_error.py,sha256=OhEWgiU5kk4QblZIhTAfAc0eU-mUKK_nTA3H-MHlJ68,593
scipy/special/specfun.py,sha256=7-MuCncWBe0xHf_iPWACck2cJ8uR7cyeoMFMD5PtXxw,612
scipy/special/special/binom.h,sha256=mpX5OYWiTYK5_idRQx1LGnpQw0W3WbY3wmQK9ABowP4,2576
scipy/special/special/cephes/airy.h,sha256=Fz3sDik1isvWTjKort8mrFRZEbiMyCpZJAdjQmXYYqk,11408
scipy/special/special/cephes/besselpoly.h,sha256=QovZIXtW-BFYLtWzvR5TfDX200j0pQFjEjKbWfXVVsY,1442
scipy/special/special/cephes/beta.h,sha256=i5GDczceZRXCeo_zHuT8uN539WhuBGv-FHqgRHaP3Nw,7267
scipy/special/special/cephes/cbrt.h,sha256=_zm07jb4cBMRMIuHw7jx4C8pw2U0pdAc7w-CKzKhWmI,3526
scipy/special/special/cephes/chbevl.h,sha256=dLyjRMFHoD-guaeTuFz1p4my6d3PxFfwAmwRjZerHhM,2003
scipy/special/special/cephes/chdtr.h,sha256=cgiATxjreL9FCG39xpvtqgQAxLnAlwCSczXVsrpS5R0,4260
scipy/special/special/cephes/const.h,sha256=R6KVg8Vp16AAccB4WlzvZUagwHYTb5ip95WmG9ZbnUo,3338
scipy/special/special/cephes/expn.h,sha256=CEBQ6bjXeTC86cv66v59YxEhd9EIZlVa1VGEij34GoE,9216
scipy/special/special/cephes/gamma.h,sha256=6rMgXKVy0ZcNFCpaKjCbF5062UdHVMzx6Hma9b_15qc,11664
scipy/special/special/cephes/hyp2f1.h,sha256=RLpQr4bICXaw1F-ql3eO6Rb6Vbp2CakQPZff5be7lEY,20665
scipy/special/special/cephes/hyperg.h,sha256=hgH1EG_mPAwPrEze8SKsTo1uKdD4UgmakOmx4fFLQRM,10835
scipy/special/special/cephes/i0.h,sha256=a7hSYRQmnrFIrRXlI0Rz8ZBnXQbO8YqP-f_bDXH3TJU,4713
scipy/special/special/cephes/i1.h,sha256=IjOJRYHvoJ0iCNViSjqUXW8OXK8W9yElt2CwfPzgdeM,4914
scipy/special/special/cephes/igam.h,sha256=Nh-5i2D5xbBMfc7xpYl6ZNqhHjKC-BWi4C7KkUxyqPQ,13386
scipy/special/special/cephes/igami.h,sha256=GqraxWglzfqHZngG-jn-QZPYJ5lerAAu62z03maVwrs,13048
scipy/special/special/cephes/j0.h,sha256=5gCuwjIj3WMXH4mOjTHD1r-qr37U-P3dLNP9qUErnx4,7119
scipy/special/special/cephes/j1.h,sha256=2ZIg0guyrOBL5qd3lYccaTYzOXD-X75q7PjfnvzE6jQ,6272
scipy/special/special/cephes/jv.h,sha256=ZOfFCalktaeStfiUjP9mSlhyagQDBJQYzR21_07W8LQ,23937
scipy/special/special/cephes/k0.h,sha256=IThL_ksLFz8SInJfgJgqQmc2qJGoZyeFctuSkGqeZU4,5044
scipy/special/special/cephes/k1.h,sha256=Y6nv2U0s8_ZSOI3ILIKS1FNOyHHdN7H3QxG-CtzGrJo,4805
scipy/special/special/cephes/kn.h,sha256=c0-zUKBWNyhXCnNCrAsmh_oSrQQxeoAk0L1rBp9AIfI,6519
scipy/special/special/cephes/lanczos.h,sha256=Y98PORDi4J5xQzYZjuYdhPkUf9tRUH1R9FPe09VbOOI,5630
scipy/special/special/cephes/ndtr.h,sha256=u6jBiYJT06cdMT5OOCPG2-AFDxis6nmqXFfRHahLJ_E,6986
scipy/special/special/cephes/poch.h,sha256=bTWulyaZNneP_wYg7SJWMOfyuPlFf3YMQKsyOWNnOxY,2484
scipy/special/special/cephes/polevl.h,sha256=6X2iZYZJiteQzIb3ZGOIQRWeDdr7oD6QzIbUR4Mto8s,4262
scipy/special/special/cephes/psi.h,sha256=GogtnrbO47yZflv3r19dUkamjtpPbkqrZzS1ikGqKH8,6517
scipy/special/special/cephes/rgamma.h,sha256=lXB_ppluJFFC2u8Ajq8HImFrV0wbfVDBLKkk7Gl07qQ,3795
scipy/special/special/cephes/scipy_iv.h,sha256=NVQWqiQ5m4L1_FVoGFGjKmZoStOXL5HnqAzXC_D6OAU,26333
scipy/special/special/cephes/trig.h,sha256=qc7JbNW3EmTt4IrbSynqoqAH89rl9RzoCqbAe2qiD2c,1414
scipy/special/special/cephes/unity.h,sha256=0qx_iVmyqd8KmePLEdGYNS0woXP18jed94v9nP8inww,5275
scipy/special/special/cephes/zeta.h,sha256=HYNQehgjT9mrS7gxHtC7se6a2UN-IFmprCzVbPhzgks,4565
scipy/special/special/config.h,sha256=fMnwIM1qsHvQsnk92BtDWQzF7iKPk7F3hLDsud2lyo4,7284
scipy/special/special/digamma.h,sha256=1ra4XP3q2geHp1RYe9kMSHs5bawUSkmwWB4ZT3eU-t0,7797
scipy/special/special/error.h,sha256=L_KzcuTi-ebmQSoGVYhWc5baYUbxAT_aZW-kC8HvsME,1953
scipy/special/special/evalpoly.h,sha256=BIBKLyczCRZ7fKZSlybuQptKN-zcL_PbkL-sgGEJnKI,1178
scipy/special/special/hyp2f1.h,sha256=qhE8gKxa2zZsI9H66jNSh7c0QCEVGjpa6i4jo5vXnUM,35563
scipy/special/special/lambertw.h,sha256=nhYHt2MtVhsE30p78zzAo3Pj23mlBpF5vJRtIphC2C8,5589
scipy/special/special/loggamma.h,sha256=HYJVjrBXCeaC5LUiTobzVxrA3o0_LdErlVXdunAozpc,6424
scipy/special/special/tools.h,sha256=3qvo29iC0PQoJ-2KeFdio59ZwzPSGl1WqFKpkthBbZ0,9486
scipy/special/special/trig.h,sha256=2FxW0q2d9SgYg5PkPYBp3OuXqxr4sL3JKZJRwAEIxOU,3305
scipy/special/special/wright_bessel.h,sha256=Wm1TZjKv4m-vXkYEdKxOPlqLgvDP618evPP5DZowYT0,43453
scipy/special/special/zlog1.h,sha256=Qa_Am2ENtaj474Ty3q9zjbt3BTL5pPLtk4TiKZtBvEk,1012
scipy/special/spfun_stats.py,sha256=mLQQYMHf6QowpGShlLy4CDL-Zri1zwyEawpx1U_f51s,552
scipy/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_bdtr.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_boost_ufuncs.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_boxcox.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cdflib.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cdft_asymptotic.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cephes_intp_cast.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cosine_distr.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cython_special.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_data.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_dd.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_digamma.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_ellip_harm.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_erfinv.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_exponential_integrals.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_extending.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_faddeeva.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_gamma.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_gammainc.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_hyp2f1.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_hypergeometric.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_iv_ratio.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_kolmogorov.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_lambertw.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_log_softmax.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_loggamma.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_logit.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_logsumexp.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_mpmath.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_nan_inputs.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_ndtr.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_ndtri_exp.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_orthogonal.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_orthogonal_eval.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_owens_t.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_pcf.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_pdtr.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_powm1.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_precompute_expn_asy.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_precompute_gammainc.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_precompute_utils.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_round.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_sf_error.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_sici.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_specfun.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_spence.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_spfun_stats.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_sph_harm.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_spherical_bessel.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_support_alternative_backends.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_trig.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_ufunc_signatures.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_wright_bessel.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_wrightomega.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_zeta.cpython-311.pyc,,
scipy/special/tests/_cython_examples/extending.pyx,sha256=FW_hJh5PYqGgpJGy3_J8LZjXf9XLMsMzDu7wbGxiyv8,304
scipy/special/tests/_cython_examples/meson.build,sha256=jAyMIESAHLDCDkdFfJ-kJAOCRtNO6JBD4LdDfE5ZVUU,552
scipy/special/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/data/__pycache__/__init__.cpython-311.pyc,,
scipy/special/tests/data/boost.npz,sha256=9W9uqJTP5Z7aJ23carRakXSwFWEt5x_6RDvsI9_rAw8,1270643
scipy/special/tests/data/gsl.npz,sha256=X7Vf4jA3wye4vNm0C6X9KNosK2_NSzPBxGwR1EYmvag,51433
scipy/special/tests/data/local.npz,sha256=ykTwo3nEwxCbDrGbvE7t-kjbmxQ-IJaoNyuXTdwOEFY,203438
scipy/special/tests/test_basic.py,sha256=0gtleYcGfjE35bpwzxOl8kl_nArfeys8gi3vaOwz7ug,178034
scipy/special/tests/test_bdtr.py,sha256=ZPWchkB2hVjJ7MALAN9K4QWI8PeGSxcqduER3_myJRk,3343
scipy/special/tests/test_boost_ufuncs.py,sha256=MCmpPb6Ab87OH6Ibzv_v2Qdr6hHleoyU4QH5WAuDvZs,1674
scipy/special/tests/test_boxcox.py,sha256=Mi2LR1O_Oc93R-5H7683VxSc22bqHssJgorQv3dDj8k,3239
scipy/special/tests/test_cdflib.py,sha256=ZeHdSFK8VocclJo2fPtZjj2O37f2wBwaCJM8U7aVi2o,17968
scipy/special/tests/test_cdft_asymptotic.py,sha256=dsIl7h0WEtzX7Y3vCZSMEKpwb4EZoHvwL4j3BvRd8Ek,1490
scipy/special/tests/test_cephes_intp_cast.py,sha256=INhKthUJ4kV3FLmbWJJothJ7NHyBZJiRQgfPA81Jm2U,1158
scipy/special/tests/test_cosine_distr.py,sha256=ENyfDI4OabouefeuzVJQO7UsBE3Eaorv39Z4GQoBc7E,2773
scipy/special/tests/test_cython_special.py,sha256=weARzTR5wY6KVHvPTVdX1j0lefRWjdzY7ZDXl1VTsYI,19312
scipy/special/tests/test_data.py,sha256=wOXl57xV0qwgsg7HGH1nIR8MZ7N_Rv3gKdrIRCVLKN4,30994
scipy/special/tests/test_dd.py,sha256=KObFRKa8cqUp9VOGBMln1UG1UtMZqSbW9fqFu9yTWKk,1606
scipy/special/tests/test_digamma.py,sha256=vBfs2G9WcIEP8VGpGaCsFlTD5g3XODAP7gsUEmAq6Ho,1427
scipy/special/tests/test_ellip_harm.py,sha256=qOVC4b0N6y7ubJNYa4K54W4R5oiHdWghdhy_rO5_gac,9918
scipy/special/tests/test_erfinv.py,sha256=JMvb2hWNQ63uPevk3kAL6KypAtOMJzg8n1G3Om9A15c,3148
scipy/special/tests/test_exponential_integrals.py,sha256=cPxuyehzPSgv_o3Q1Cfb2ON5NNxgBgyAi8dCDILIQe0,3805
scipy/special/tests/test_extending.py,sha256=4APcA5IIUiivKFTIiR82xcDuqP10vVv7AAwZydDlHLg,1029
scipy/special/tests/test_faddeeva.py,sha256=2BXe8JR2d_QBMvjX-bmHkP0TDzy4JgZhM6XvSlqO1HA,2653
scipy/special/tests/test_gamma.py,sha256=-u9wzNPSPsJTs_DJL6VZAXXIcIqJsxOeQy__MdRTGxY,270
scipy/special/tests/test_gammainc.py,sha256=Rrv_OnC7U6EtXSvrbGQpcPb7sccWVPIgIAzjs6DBIxM,3951
scipy/special/tests/test_hyp2f1.py,sha256=Y1n02TTI2bD9aei6UifPw9EgCZE3Cb8cIcXSy34_oKs,93313
scipy/special/tests/test_hypergeometric.py,sha256=JmpspHWUTyE1IW9Fyu6Zw5L2RJFVAWDL1rsfz0E9NjE,7032
scipy/special/tests/test_iv_ratio.py,sha256=RIi66yNBSLUpDPzRZ-LbifmetHybGCQ4EvsfwpC-Dzg,5499
scipy/special/tests/test_kolmogorov.py,sha256=tumJSXhfuCXak-8qGtK9xD-qFK4Yt350fVN8ezZFqWI,19905
scipy/special/tests/test_lambertw.py,sha256=AoYbanXWmx5twRL-HBARQx4RPSfHMPboSEXl0GXwNfQ,4669
scipy/special/tests/test_log_softmax.py,sha256=0VhKfjYbv5-bV_d4tWyrG6OawlRjCUi_O8mRSgW0Mn8,3524
scipy/special/tests/test_loggamma.py,sha256=SdxYiItvixR4NabOKLyBHu-iEP7R52Rew8N8bZugH2s,2062
scipy/special/tests/test_logit.py,sha256=eJfxl4AYJF2VRFebHTw5NS7eF7Q_R82C42x3cou7uwE,5685
scipy/special/tests/test_logsumexp.py,sha256=BPoLa4WLGO8AJxjY1j0YQ5SsKq1LM-vNAtzEvh-m_tY,6863
scipy/special/tests/test_mpmath.py,sha256=9gFGLgpPaUx1riEWjNZsBO5Uu-uNipQYWcoYZ-N0Jcg,74937
scipy/special/tests/test_nan_inputs.py,sha256=Zgaxcg14kZegSoO4t_vIIFs1c9ZYiTU3gNoHgGVYJgM,1895
scipy/special/tests/test_ndtr.py,sha256=n_Y8wG9MYVmZOXPgJoiYjPGlUULdiPoDQFsuR6HzPWg,2757
scipy/special/tests/test_ndtri_exp.py,sha256=b4vFFkhdzIDWNgmKOEy5UjRCYtHsdkoaasRi_L9lzIM,3802
scipy/special/tests/test_orthogonal.py,sha256=jGOpJz_awB9KOkMMvu3denxMmxILvY6Z57d-3R2VU5s,32342
scipy/special/tests/test_orthogonal_eval.py,sha256=cYOfiw2FrWSFFnilzR1YZuE1WO6sLY-0zM8gQHYouIM,9845
scipy/special/tests/test_owens_t.py,sha256=sotiVxRrjoQ4lYjbbBx88nQKHEHhNowfJ7KPdIM-vNQ,1845
scipy/special/tests/test_pcf.py,sha256=VTEqviYLsm0DBnuu1Bpyn6rEocf9sZGSyY12ufwxaYo,688
scipy/special/tests/test_pdtr.py,sha256=YpZQ7ssQ5z0yL2lKss4lDooRlz_3n-aY2q4a8aVXXB8,1332
scipy/special/tests/test_powm1.py,sha256=AG5D4ex4jCS97xN7RlyumVc09OUTuczNxS2IYwt9OAg,2341
scipy/special/tests/test_precompute_expn_asy.py,sha256=DFW5CiFXjHNUkWgZrehKUxPNTd09Cllp1nZ0_FU172g,607
scipy/special/tests/test_precompute_gammainc.py,sha256=sY-xFU6MwT-bzjlXXYqu2_spch0XGJO0sZR6pQHMgPs,4567
scipy/special/tests/test_precompute_utils.py,sha256=EWXMuVz5WDUQISPzh87i02u1im9TcLbDBxyk-BO6iZY,1201
scipy/special/tests/test_round.py,sha256=lvMBZ12o4FLVs4BdO8y64bm4gBOT0DCedccFfrml8aw,491
scipy/special/tests/test_sf_error.py,sha256=Vg_l2Gzq-m7pDfS9fUEWTueGdKDH5x4LJYWJx_sanKw,4273
scipy/special/tests/test_sici.py,sha256=REYntiqC0T4Pb-66ESBmjEmIoTJDbjYs786XNwUkzIw,1263
scipy/special/tests/test_specfun.py,sha256=WGbAsmg4TF4qXhZ7O0LJ-PpIcbB0WazK_B37SCbJzJY,1411
scipy/special/tests/test_spence.py,sha256=r4chXBi_nKK1uSfPse7owNhvcC93aBvcMBZf_8wTlF8,1131
scipy/special/tests/test_spfun_stats.py,sha256=ArofuQVYCm6RevEx0gP2AJ1_6ARrf4Qs5jL3j0LEPKU,2058
scipy/special/tests/test_sph_harm.py,sha256=ywBendq7uw5mz_-XZDV1v-iTd6ga8Xq0sJcJQLBF5B4,1896
scipy/special/tests/test_spherical_bessel.py,sha256=_h08oRpVTjaMs5csuUavDQn5EBsjd3_kG10RbAM3dIY,14763
scipy/special/tests/test_support_alternative_backends.py,sha256=iRfqtG8nZGiVihqr7oenSJkSg19D4N59ThN0OmXxcUc,3719
scipy/special/tests/test_trig.py,sha256=vJL6u-XkIOfMOAAqAUs_xfbcJT4CG3HwF6iZ3fMmgjI,2404
scipy/special/tests/test_ufunc_signatures.py,sha256=s5AFKayOq0iSQ6d8Cwre4AUZI-JyQhgx1SSw4PUJt4w,1884
scipy/special/tests/test_wright_bessel.py,sha256=nCp01wWH-sKbQXiig9IKGSiTgwjfpVpTX0oq4dT8pwA,7894
scipy/special/tests/test_wrightomega.py,sha256=-2-tzEj7HA21xTo_WtCU5nc6rJDT9Tr0Z9IjA2f39mM,3677
scipy/special/tests/test_zeta.py,sha256=FbhrcRuDXJ_ZrVMRdUvICiaGMp7DM5CeWcOwYSKhXvk,1416
scipy/stats/__init__.py,sha256=hqTRIEfWorRnZbc9K0QRt8yTtMdh2RbOMS-J95c29f8,18919
scipy/stats/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/__pycache__/_axis_nan_policy.cpython-311.pyc,,
scipy/stats/__pycache__/_binned_statistic.cpython-311.pyc,,
scipy/stats/__pycache__/_binomtest.cpython-311.pyc,,
scipy/stats/__pycache__/_bws_test.cpython-311.pyc,,
scipy/stats/__pycache__/_censored_data.cpython-311.pyc,,
scipy/stats/__pycache__/_common.cpython-311.pyc,,
scipy/stats/__pycache__/_constants.cpython-311.pyc,,
scipy/stats/__pycache__/_continuous_distns.cpython-311.pyc,,
scipy/stats/__pycache__/_covariance.cpython-311.pyc,,
scipy/stats/__pycache__/_crosstab.cpython-311.pyc,,
scipy/stats/__pycache__/_discrete_distns.cpython-311.pyc,,
scipy/stats/__pycache__/_distn_infrastructure.cpython-311.pyc,,
scipy/stats/__pycache__/_distr_params.cpython-311.pyc,,
scipy/stats/__pycache__/_entropy.cpython-311.pyc,,
scipy/stats/__pycache__/_fit.cpython-311.pyc,,
scipy/stats/__pycache__/_hypotests.cpython-311.pyc,,
scipy/stats/__pycache__/_kde.cpython-311.pyc,,
scipy/stats/__pycache__/_ksstats.cpython-311.pyc,,
scipy/stats/__pycache__/_mannwhitneyu.cpython-311.pyc,,
scipy/stats/__pycache__/_mgc.cpython-311.pyc,,
scipy/stats/__pycache__/_morestats.cpython-311.pyc,,
scipy/stats/__pycache__/_mstats_basic.cpython-311.pyc,,
scipy/stats/__pycache__/_mstats_extras.cpython-311.pyc,,
scipy/stats/__pycache__/_multicomp.cpython-311.pyc,,
scipy/stats/__pycache__/_multivariate.cpython-311.pyc,,
scipy/stats/__pycache__/_odds_ratio.cpython-311.pyc,,
scipy/stats/__pycache__/_page_trend_test.cpython-311.pyc,,
scipy/stats/__pycache__/_qmc.cpython-311.pyc,,
scipy/stats/__pycache__/_qmvnt.cpython-311.pyc,,
scipy/stats/__pycache__/_relative_risk.cpython-311.pyc,,
scipy/stats/__pycache__/_resampling.cpython-311.pyc,,
scipy/stats/__pycache__/_result_classes.cpython-311.pyc,,
scipy/stats/__pycache__/_rvs_sampling.cpython-311.pyc,,
scipy/stats/__pycache__/_sampling.cpython-311.pyc,,
scipy/stats/__pycache__/_sensitivity_analysis.cpython-311.pyc,,
scipy/stats/__pycache__/_stats_mstats_common.cpython-311.pyc,,
scipy/stats/__pycache__/_stats_py.cpython-311.pyc,,
scipy/stats/__pycache__/_survival.cpython-311.pyc,,
scipy/stats/__pycache__/_tukeylambda_stats.cpython-311.pyc,,
scipy/stats/__pycache__/_variation.cpython-311.pyc,,
scipy/stats/__pycache__/_warnings_errors.cpython-311.pyc,,
scipy/stats/__pycache__/_wilcoxon.cpython-311.pyc,,
scipy/stats/__pycache__/biasedurn.cpython-311.pyc,,
scipy/stats/__pycache__/contingency.cpython-311.pyc,,
scipy/stats/__pycache__/distributions.cpython-311.pyc,,
scipy/stats/__pycache__/kde.cpython-311.pyc,,
scipy/stats/__pycache__/morestats.cpython-311.pyc,,
scipy/stats/__pycache__/mstats.cpython-311.pyc,,
scipy/stats/__pycache__/mstats_basic.cpython-311.pyc,,
scipy/stats/__pycache__/mstats_extras.cpython-311.pyc,,
scipy/stats/__pycache__/mvn.cpython-311.pyc,,
scipy/stats/__pycache__/qmc.cpython-311.pyc,,
scipy/stats/__pycache__/sampling.cpython-311.pyc,,
scipy/stats/__pycache__/stats.cpython-311.pyc,,
scipy/stats/_ansari_swilk_statistics.cp311-win_amd64.dll.a,sha256=9GlrYjKw98nEv2NQlBzbpElgiPHv4fnFOrO_I74oyrw,1752
scipy/stats/_ansari_swilk_statistics.cp311-win_amd64.pyd,sha256=rcUTwcrD13DXuTzu7TzKzjhUESlyZC7O3iW2unRZNko,256512
scipy/stats/_axis_nan_policy.py,sha256=qvxsde9LQt8cAxnlHiQKwLlWCiib3c-gZbni7F8a8d8,31760
scipy/stats/_biasedurn.cp311-win_amd64.dll.a,sha256=9DWvj1XRNXR2Fehazs81tQsH9wegldB22QfsOGhpELk,1580
scipy/stats/_biasedurn.cp311-win_amd64.pyd,sha256=jTV_uv3_R6X5T9HhvteTLKdVVGvr0PyVe_g4OwLzVc8,358912
scipy/stats/_biasedurn.pxd,sha256=OHa5weTjmvbIdkvmgDnD021p44bVaS5mNvDIx562SLI,1073
scipy/stats/_binned_statistic.py,sha256=IDN2tbE4N5CAl0DvjUmF0ChKSe0bsYdzCOCGfgtgIbs,33507
scipy/stats/_binomtest.py,sha256=gXacpbMuFCBgpwG_1__1InZhMgZsPfDZ3FFETkPybQQ,13493
scipy/stats/_bws_test.py,sha256=h-6Ra_vazN0T6FA1kHZkG1AkEQ6JiRkFzJwVxD5ghS8,7239
scipy/stats/_censored_data.py,sha256=-mOWeG-hGYqCz8sjbELiBeBOE216wS8PsvmD99YNVxA,18765
scipy/stats/_common.py,sha256=PUBtrtKESxYoaVx3tlq8ZYImEYCIuYslFYikUS7uwdU,177
scipy/stats/_constants.py,sha256=AZugmM-2GSPX31Ppxo60H41VzwhlMFuanU8tpXAKFLM,1001
scipy/stats/_continuous_distns.py,sha256=WEqS6AsW44TwDIa9zdgy0DOmxgEEwytBa9KctKhLZ4A,408008
scipy/stats/_covariance.py,sha256=quuUd1AqbdCW9b83koE_XC40nAxhtlAJ4M5O8OqLrYg,23157
scipy/stats/_crosstab.py,sha256=Tl9fsThqNdhFfwuIkXoZHd6lzBB5z0jug5uLLecIq6k,7551
scipy/stats/_discrete_distns.py,sha256=ADpv3cO3ozf-BkC_rs1HKdUNIgq5e-LOeEbBDB0BchU,59773
scipy/stats/_distn_infrastructure.py,sha256=6TgzOOiFAqkggsrGvR-rOn-JwARqUEOxkgeYDd6BVd0,154219
scipy/stats/_distr_params.py,sha256=fzwmPbMVzswRaPqPNdtcmjcM7h5Am10eS1xVb0nHNrQ,9128
scipy/stats/_entropy.py,sha256=EHZVyEiTKdmbdiiqFFrBoHD0BwZ6SbCNjZDkZsnxYdQ,15816
scipy/stats/_fit.py,sha256=jqLp7JXgDPZtLNZlflzAkCZ9IZROoG1EDIeninNOXrk,61234
scipy/stats/_hypotests.py,sha256=OcVXANxCfpxwkvV6qANuZ9yf1-Uoo8NyrQFsDzZxO0U,81186
scipy/stats/_kde.py,sha256=Ftbpe4Ay3b0oTw80N5X2f1GH8tssj9Cbn0_D7-vTJA0,25762
scipy/stats/_ksstats.py,sha256=_buNl_2SqBDhPu318J-5zf58HNFvLsJkgjLINC6tWts,20716
scipy/stats/_levy_stable/__init__.py,sha256=XV3OS5J44tPRYHO6O7KPyx1P3BfEpKoN9tGVHXbgWeA,46765
scipy/stats/_levy_stable/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/_levy_stable/levyst.cp311-win_amd64.dll.a,sha256=XcBX276xUCC5lcfvNP-Nnm_hGHWIy953pEZR4BTg7O4,1532
scipy/stats/_levy_stable/levyst.cp311-win_amd64.pyd,sha256=Zdob2h1b7NrtbHS6-LCLI7mz3OV7uc3NMfXat8mW8vI,56320
scipy/stats/_mannwhitneyu.py,sha256=UmxdLeGeI_0Ebl6gn8fUIGNmUF8tXKV4_zoredISGsk,20066
scipy/stats/_mgc.py,sha256=XdXnhwswwL6TAsMYN0VHOMEVvkj-hqgiN-yRJUrARU8,21874
scipy/stats/_morestats.py,sha256=EuRQn8ky3wSR510O61oJ6XURITZkDJGsmsHUKgtTo3A,195117
scipy/stats/_mstats_basic.py,sha256=axcXKptREaby_cOVW4tf2E9eExMikGk8c4skwnt9438,126619
scipy/stats/_mstats_extras.py,sha256=mH5y3bwAEO6XhyvIAsfitMwxmqsdD1hKbvQl8EhqMuY,16891
scipy/stats/_multicomp.py,sha256=a0QNETPUdVz-muYize7NnHIB50LpMGkXIfNiBbyBjhM,17741
scipy/stats/_multivariate.py,sha256=n9t--CSA5jGCmj4p0m6dhGV-IsQFWiR5u8OcYOYrT34,244828
scipy/stats/_mvn.cp311-win_amd64.dll.a,sha256=3SxN6D7JI26b_glkiZt9u762iR_lLEj__3SfMSWY2Dc,1512
scipy/stats/_mvn.cp311-win_amd64.pyd,sha256=G9eOJ8sLTgs5Kc3DHagwZm3aGHR5iTnp3z47rCyyH_M,105984
scipy/stats/_odds_ratio.py,sha256=H6kt97vVOAgmD-Ipy9Tgmbezi1rHvzgJ_5ie0OMd87Y,18343
scipy/stats/_page_trend_test.py,sha256=dvnRV0v6KZvrZyLT3w7nkp02dkv5T54C4ZcTDFt-iy0,19466
scipy/stats/_qmc.py,sha256=RANo7ztG8YAcRHHGCQEnyVap3xZeK8iDILLktvmBD-s,102119
scipy/stats/_qmc_cy.cp311-win_amd64.dll.a,sha256=L61_xL40tgmntOQ6VOgeLj2k2ERKXdZijysWYxpkKJg,1544
scipy/stats/_qmc_cy.cp311-win_amd64.pyd,sha256=k49AXFTqn_tajbfkyZEHlfnfY2YICDOirSoTJEo0LNI,407552
scipy/stats/_qmc_cy.pyi,sha256=l2173UiDribq_G9S47QozpcKo8nO9D3rojwKLEd8m8k,1188
scipy/stats/_qmvnt.py,sha256=nlJZeatM_ZrfvGrZ5NKGPc4pP-g7TbdMj0aDdvVY6Z8,19300
scipy/stats/_rcont/__init__.py,sha256=xtM2CGKxJ2uUfF6Pfyoi_0em8i5pa6njGRaaGSrvFkA,88
scipy/stats/_rcont/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/_rcont/rcont.cp311-win_amd64.dll.a,sha256=Lp7jC45Vnthfro70MEu3XCcE3Ty9GqARqqsOiV29OmY,1520
scipy/stats/_rcont/rcont.cp311-win_amd64.pyd,sha256=KMpncKJk07rOuD7iPdF5CshdR6WlJ7fSF_txisAfLfU,237568
scipy/stats/_relative_risk.py,sha256=txlXt8-p2wLydMIrKEOwdAt5giral_X51T2itd7VDDw,9834
scipy/stats/_resampling.py,sha256=N-C7XdNdTnMbnc2Jjb5F7lxsCwZC6hl2BNIB26g7AwY,100741
scipy/stats/_result_classes.py,sha256=904WSGrKwzuWHdd3MaS-HUwTJzLm8iF5aNzh0wZq-TM,1125
scipy/stats/_rvs_sampling.py,sha256=2wz3p8laZ60p2ow9H_G5hnj5ZFlulaMynnFE4laJYRc,2289
scipy/stats/_sampling.py,sha256=6pcrRPLvRFI-VRM4gRuw8Fub2l8Ot8WeyC-o_AEM3KA,47722
scipy/stats/_sensitivity_analysis.py,sha256=ZU5scZRNF5evHDM2ViyRKeRIeJskKXgpmObkEctohpw,25457
scipy/stats/_sobol.cp311-win_amd64.dll.a,sha256=QItgHcBCJqsgYIuiocZNew7QPN4UztSUXM6ZShMtUE0,1532
scipy/stats/_sobol.cp311-win_amd64.pyd,sha256=MRW5nw4ql0_uCSmBLEWmd8P8L8gvy-slynBWk3S1P_A,371200
scipy/stats/_sobol.pyi,sha256=tskim056aVi6WIymUMEMdWHaLk8QMqWa2kCAbF2ZTPo,1025
scipy/stats/_sobol_direction_numbers.npz,sha256=SFmTEUfULORluGBcsnf5V9mLg50DGU_fBleTV5BtGTs,589334
scipy/stats/_stats.cp311-win_amd64.dll.a,sha256=jtDmWIvEtT0imy6oXWznvmlLVcljleeDLy71Vaax6AM,1532
scipy/stats/_stats.cp311-win_amd64.pyd,sha256=C5a2fbU5Z0JZAmgn-9jlWZ4aCtmQrUE007mvd52-cM4,693248
scipy/stats/_stats.pxd,sha256=EU9o8xwL148qbx8h0aI2Uy-lUtZ3i683HIRZRkTvjHM,719
scipy/stats/_stats_mstats_common.py,sha256=uzD2i2higUX8-we7sMBJ1RLGuIQeJ0FMglwvOm0d2sM,11860
scipy/stats/_stats_py.py,sha256=FsZpCUKkQmIC9HVkgcfq1bKbyVkSgrwVift0fk6r_wE,431367
scipy/stats/_stats_pythran.cp311-win_amd64.dll.a,sha256=l6s_DhAa4OAvW5xVdshzCJStfjcLwqR24i6TsE_NcVE,1628
scipy/stats/_stats_pythran.cp311-win_amd64.pyd,sha256=9o2jlmf9nnkyww_J2FZRk54PDncIZ4yLerlXzigfPiY,1067008
scipy/stats/_survival.py,sha256=UduwXr9e9TayQMVe5X_0Ca2NfaXdOdamO7OfWoLl6g8,26676
scipy/stats/_tukeylambda_stats.py,sha256=RtXsm72WCNJ8kQSYkL6x5XEsRopLVkY_wUSS_32NtmY,7070
scipy/stats/_unuran/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/_unuran/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/_unuran/unuran_wrapper.cp311-win_amd64.dll.a,sha256=a2i8RQ0tfkOQ0wVh1k7VP6bZUfAH2hB7cCT0UlOi8Ok,1628
scipy/stats/_unuran/unuran_wrapper.cp311-win_amd64.pyd,sha256=3N5wujuxrhRE1fzGtc80ZWTmC4WboytMk8C7PlAQeAM,1470976
scipy/stats/_unuran/unuran_wrapper.pyi,sha256=Hz75eHJFm2oUm6_Qg88-PxKZX8hjBuGUAw3nOp-1N7o,5802
scipy/stats/_variation.py,sha256=M230FqSkmVzriVd-WEQx5SxW9baxGRsVj4F8L-G6zAE,4780
scipy/stats/_warnings_errors.py,sha256=f_Hxg3GzZJW2U1B608HFABSkV2w9Xjv1qtsND2AuGKA,1234
scipy/stats/_wilcoxon.py,sha256=d2IHHx1sdY2-6hONKu5EC4xZTxEJovyygqQblC0PGUs,8838
scipy/stats/biasedurn.py,sha256=gb1UJyRCcvPzqliBEPcPBYUPrIha8ztIOXphhQc6QG4,447
scipy/stats/contingency.py,sha256=SxMGmqxWVIHJIRRJC2tI_aeU0mE-JnMx1PXoQLLtPQQ,16743
scipy/stats/distributions.py,sha256=_nRpDudL-PbnWvNm3fxRQ-00zEtCleSqhTNk1evZvq8,883
scipy/stats/kde.py,sha256=CtU4WVO1iuK6f80cn8Lz8z75OXwE0z4BevdRy_o_bP4,534
scipy/stats/morestats.py,sha256=2qX7gBOG7_3PqAtwLS0uM1198ehUm8z8u8W2pikxaYs,1000
scipy/stats/mstats.py,sha256=jzvELjOG5FulzAORL52iNL6VwQhA3N27RAr_-8fSu1Y,2606
scipy/stats/mstats_basic.py,sha256=4GSiBxQUMXnsBtFoV8BW6etxocpU69xfLzLFHO1gt9c,1436
scipy/stats/mstats_extras.py,sha256=OEMN7L4PfGARho8qIF9fmUg6TXnQcWBVWBHLeZ7m1l0,746
scipy/stats/mvn.py,sha256=WopFz0qBdEbGzhKk7eGfFVa9ojHkg2aKn3nzOjAkq4s,515
scipy/stats/qmc.py,sha256=tm9AkmRLZQCSc9HDwXywCd4b_lcxqQZbeRsNtqTWfEY,11939
scipy/stats/sampling.py,sha256=DB2ePAYSuu7z4FH2CcSSW9ifDWTQi8q1lTbpNitCxCY,2012
scipy/stats/stats.py,sha256=N2DCCNuXdZJ_EKuON0UCUF1gu_S8p20PEaJTCfoPfg8,1553
scipy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/tests/__pycache__/common_tests.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_axis_nan_policy.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_binned_statistic.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_censored_data.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_contingency.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_continuous_basic.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_continuous_fit_censored.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_crosstab.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_discrete_basic.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_discrete_distns.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_distributions.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_entropy.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_fast_gen_inversion.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_fit.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_hypotests.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_kdeoth.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_mgc.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_morestats.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_mstats_basic.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_mstats_extras.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_multicomp.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_multivariate.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_odds_ratio.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_qmc.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_rank.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_relative_risk.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_resampling.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_sampling.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_sensitivity_analysis.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_stats.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_survival.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_tukeylambda_stats.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_variation.cpython-311.pyc,,
scipy/stats/tests/common_tests.py,sha256=HFr0tumGtvvZ_s4wBELMxz7SC7-eD5Sl4Z-Kyxr6s_M,12786
scipy/stats/tests/data/__pycache__/_mvt.cpython-311.pyc,,
scipy/stats/tests/data/__pycache__/fisher_exact_results_from_r.cpython-311.pyc,,
scipy/stats/tests/data/_mvt.py,sha256=FhbZqSVqMei58-awIxMScB_gSDj_1Fy-RKRkJwuBBns,7076
scipy/stats/tests/data/fisher_exact_results_from_r.py,sha256=AqKIctwYj-6FUc6XhwZIfrPCEw073oMKhSWrDhuz00I,27956
scipy/stats/tests/data/jf_skew_t_gamlss_pdf_data.npy,sha256=JU0t7kpNVHuTMcYCQ8b8_K_9JsixBNCNT2BFp2RbO7o,4064
scipy/stats/tests/data/levy_stable/stable-Z1-cdf-sample-data.npy,sha256=zxjB8tZaIyvyxxISgt8xvyqL6Cevr8TtgQ7TdFfuiYo,183728
scipy/stats/tests/data/levy_stable/stable-Z1-pdf-sample-data.npy,sha256=_umVErq0zMZWm0e5JOSwNOHNurViT6_H4SBki9X3oSg,183688
scipy/stats/tests/data/levy_stable/stable-loc-scale-sample-data.npy,sha256=88cZ7dVDH7nnuey20Z48p6kJUpi9GfImaFsPykDwwHM,9328
scipy/stats/tests/data/nist_anova/AtmWtAg.dat,sha256=WhCDyQYtOWbyRL_6N2_JsDMkSr-GoNDdAWrznNcfuKc,3171
scipy/stats/tests/data/nist_anova/SiRstv.dat,sha256=Wk8C1_JW4Yww3K0bgeAmC7gQtawRP2EH4G3uFo0HZj4,2032
scipy/stats/tests/data/nist_anova/SmLs01.dat,sha256=rSjKwNT7ef_qJSK1U0kIO96hDxKm-MRs-6vMzbTWuJY,6304
scipy/stats/tests/data/nist_anova/SmLs02.dat,sha256=cI4y6vNnaavnUz59zRttxnCRtg35ON03xPDmVmDvews,48430
scipy/stats/tests/data/nist_anova/SmLs03.dat,sha256=4YQE0GKLeGcFgLIwo6O2zZl9lZ0A-Soog6TalCsZN3o,469635
scipy/stats/tests/data/nist_anova/SmLs04.dat,sha256=EwjrFYHyjObJ3kypUg_Mcp-RbDTx-d7hD7bmWB5bTOc,7064
scipy/stats/tests/data/nist_anova/SmLs05.dat,sha256=exMz5uVs7XQUUJbfd0z7H2MFITyyj5qCIsQYyoXQwpY,55668
scipy/stats/tests/data/nist_anova/SmLs06.dat,sha256=DA8Jot8F8unbUySmqu7YVkdlHodOn1w4krjyn8SiAPc,541674
scipy/stats/tests/data/nist_anova/SmLs07.dat,sha256=_ZXFEKqLPOf2fd0yhGzOrX2auuRBeQM2xvsPywrjVY8,7630
scipy/stats/tests/data/nist_anova/SmLs08.dat,sha256=h9043__rZdOdvj6LpzvRZkT8OWwarQP8ApUiXzumrvk,61097
scipy/stats/tests/data/nist_anova/SmLs09.dat,sha256=9GiMWg1WkPyG-ifgxaJl7vCyRkHnSvJ6py2WFQhN0T8,595702
scipy/stats/tests/data/nist_linregress/Norris.dat,sha256=WraQbGipxLLrFmHAzX0qCfWkaabU4FtraR8ZD197lE8,2688
scipy/stats/tests/data/rel_breitwigner_pdf_sample_data_ROOT.npy,sha256=7vTccC3YxuMcGMdOH4EoTD6coqtQKC3jnJrTC3u4520,38624
scipy/stats/tests/data/studentized_range_mpmath_ref.json,sha256=ZIARw6MmSmQkPpmLKGurnF_WxcZqQxUJmgoVsooTJIU,30737
scipy/stats/tests/test_axis_nan_policy.py,sha256=NALGK2RqAb3Qn_GhKR81dwYKxpPT1COS7KEHLqcA3Cg,57480
scipy/stats/tests/test_binned_statistic.py,sha256=Zx8gdkQGNDoF6gUXLbi3PJdXsN-4ZmVrB_4HnCcYYYY,19382
scipy/stats/tests/test_censored_data.py,sha256=LtTBWL6HY-0m7HzbZkYDLO1NH3Z0h_D7I2XZ27CwuWY,7087
scipy/stats/tests/test_contingency.py,sha256=IXGuId-yDBKkt3aYl-Qj16Ojl_hFKQ0NEkLjfFCzlZ8,7947
scipy/stats/tests/test_continuous_basic.py,sha256=ryltYxXCRE0UfNfgjVyBmeOjzYQGdeXqyyrDa_UDVY8,43812
scipy/stats/tests/test_continuous_fit_censored.py,sha256=PtKzuvJPtFTF__pr209v-i34pcYOXSCB1vMfz8Uez3M,24871
scipy/stats/tests/test_crosstab.py,sha256=mfrrI2cyyW5XpSrFpGI7oqcSBfbzxVJkPCv1TOO9NoU,4021
scipy/stats/tests/test_discrete_basic.py,sha256=vopf0Ao7tANYkEkle8hhJrSgKwufTEu7fuk2iUK0tkA,21130
scipy/stats/tests/test_discrete_distns.py,sha256=FqKg-bWb7JoSw0QkmgTbjKPqE7YwS593FgOZ07rnht0,23881
scipy/stats/tests/test_distributions.py,sha256=ptKjgfYWmzDJ1bLoSiPby_Z9SwbG2L6GNUk2LwRI_Bs,399716
scipy/stats/tests/test_entropy.py,sha256=dE86DJFpr5__UQAVVdUvqNX8fQx5YH5T4v_cvhWQuJQ,12340
scipy/stats/tests/test_fast_gen_inversion.py,sha256=HAO2sSpcAwoZ_o7OpnquO0DL1WLNmH88iNjcoDcMRy0,16341
scipy/stats/tests/test_fit.py,sha256=awPi7qqMJITJwqKquYXUnispS6Y3RQrmhv791LhL11Q,47509
scipy/stats/tests/test_hypotests.py,sha256=unVcX_WPqeNZqumo1fiAaEWoBQf-bworGMVH5NdM3ps,81740
scipy/stats/tests/test_kdeoth.py,sha256=IyKgeI1CuDwu2lv3W9q_NCuz1djxSmk8vwR461EHwzo,21080
scipy/stats/tests/test_mgc.py,sha256=6mGp3ER4zTzVmY6NhzdTuSpGOMJpdYFlDev9dsqeobM,8178
scipy/stats/tests/test_morestats.py,sha256=Eby28uWwGwXLnvli-Za7y40mBYmmCSBSvhr9_zILJ4Y,139149
scipy/stats/tests/test_mstats_basic.py,sha256=G1fvGm5gNgIeecelZVmh5jedwMOR7-W3PyQ8CduHsIY,88998
scipy/stats/tests/test_mstats_extras.py,sha256=ae8Qn_-iLAI70UaMDbl_r78eeX2xVShuNrRXa81jMxA,7469
scipy/stats/tests/test_multicomp.py,sha256=zabamhTMhUvYbxWS7vHQEPXuFJv6FAg5czBhVVYbchs,18230
scipy/stats/tests/test_multivariate.py,sha256=DzeHae69isnuavLMl1s4o--cnKgQU4epOCsOc4AVPXc,156957
scipy/stats/tests/test_odds_ratio.py,sha256=UtuPRXBu9QTZ-gMynkqB1XxqJs0A3ldUBPh0wmEcjww,6875
scipy/stats/tests/test_qmc.py,sha256=J4ce-tnYOEDchvb1zge0Xx2LtVkLZNgsqnfIewX8O-4,56055
scipy/stats/tests/test_rank.py,sha256=aAHd5i2KFh0i-urgQW7uw6bLIkdMwQEbDvPcoLIHdjw,12131
scipy/stats/tests/test_relative_risk.py,sha256=_JizDcNuNxqEQ5j9fNqbvhu0Xxa8YkDUlBivVTsykhY,3741
scipy/stats/tests/test_resampling.py,sha256=4VtrlNl-m1Yk0zNTYcI1bpc7rMw-d3aPIfNKp2kWc3M,85649
scipy/stats/tests/test_sampling.py,sha256=y1By-le1JTNkEWPsBUEPKhKClYNbTkUW2f99hVKBnOQ,56004
scipy/stats/tests/test_sensitivity_analysis.py,sha256=fBvvNJSElLO9RkZ5R3L6It1osIITZOe2x1tyjYC2BQs,10458
scipy/stats/tests/test_stats.py,sha256=fDl5UooRtcBBQ7lKChrHBHtThLQF2R7V1WJ6HgnPevE,388189
scipy/stats/tests/test_survival.py,sha256=CyFbtihuBA5nj5-aXl_-4Ox67ehTLThNO__MJwlJ9SY,22735
scipy/stats/tests/test_tukeylambda_stats.py,sha256=AmqMknbKki17oYdfP5H_IaP6WyuvWRXNyUrWflzGLAE,3316
scipy/stats/tests/test_variation.py,sha256=IM_UQ0i9u8yUJQhzQ0SzmVRXtmoJb0DBDotrclBQcvI,9412
scipy/version.py,sha256=o2FtEt93lvkz90L_LvsYekOHIBfnBrOXsCcMR48jWqI,276
